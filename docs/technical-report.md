# Churn-Exit Survey Bot - Technical Backend Report

## Executive Summary

The Churn-Exit Survey Bot is a single-purpose SaaS application designed to automatically survey customers when they cancel Stripe subscriptions. The backend system orchestrates webhook processing, email delivery, survey management, and real-time notifications to provide immediate churn insights.

## System Architecture

### Core Components

1. **Webhook Processing Engine**
   - Stripe webhook endpoint: `/webhooks/[uid]`
   - Real-time subscription cancellation detection
   - Event enrichment with Stripe metadata

2. **Survey Management System**
   - One-time token generation for survey links
   - Response collection and processing
   - Survey template management
   - Customer Portal redirect handling

3. **Email Delivery Service**
   - SendGrid integration for transactional emails
   - Domain authentication (SPF/DKIM auto-setup)
   - Personalized email templates

4. **Notification System**
   - Slack webhook integration
   - Real-time message formatting with emoji coding
   - Channel-specific delivery

5. **User Management**
   - Stripe OAuth authentication
   - Plan-based feature gating
   - Usage quota tracking

6. **Customer Portal Integration**
   - Redirect URL generation and management
   - Onboarding guidance for Stripe Dashboard setup
   - Direct survey access via Customer Portal cancellation

## Technical Stack

### Backend Framework
- **Runtime**: Node.js with Express.js
- **Database**: PostgreSQL
- **Queue System**: Redis for job processing
- **Deployment**: Fly.io or Railway

### External Integrations
- **Stripe API**: Webhook processing, OAuth, Checkout
- **SendGrid**: Email delivery and domain authentication
- **Slack API**: OAuth and webhook notifications
- **Stripe Checkout**: Payment processing

## Database Schema

### Core Tables

```sql
-- Users and authentication
users (
  id UUID PRIMARY KEY,
  uid TEXT UNIQUE, -- Unique identifier for Customer Portal redirects
  stripe_customer_id TEXT UNIQUE,
  stripe_account_id TEXT,
  email TEXT,
  plan_type TEXT DEFAULT 'free',
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- Survey configurations
surveys (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  question_text TEXT,
  options JSONB,
  branding JSONB, -- logo, colors, from_name, from_email
  slack_channel_id TEXT,
  slack_workspace_id TEXT,
  follow_up_enabled BOOLEAN DEFAULT false,
  tracking_enabled BOOLEAN DEFAULT false, -- Email open tracking
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP
)

-- Webhook events from Stripe
events (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  stripe_event_id TEXT UNIQUE,
  event_type TEXT,
  customer_id TEXT,
  subscription_id TEXT,
  plan_name TEXT,
  mrr_lost DECIMAL,
  canceled_at TIMESTAMP,
  processed_at TIMESTAMP
)

-- Survey responses
responses (
  id UUID PRIMARY KEY,
  event_id UUID REFERENCES events(id),
  survey_id UUID REFERENCES surveys(id),
  token TEXT UNIQUE,
  selected_option TEXT,
  custom_text TEXT,
  status TEXT DEFAULT 'pending', -- 'pending', 'responded', 'no_response'
  responded_at TIMESTAMP,
  slack_sent_at TIMESTAMP,
  follow_up_sent_at TIMESTAMP,
  no_response_at TIMESTAMP,
  email_opened_at TIMESTAMP,
  open_count INTEGER DEFAULT 0,
  hover_data JSONB -- Track option hover/click data for heat-map
)

-- Usage tracking
usage_logs (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  month_year TEXT, -- YYYY-MM format
  events_count INTEGER DEFAULT 0,
  created_at TIMESTAMP
)
```

## API Endpoints

### Authentication & Setup
```
POST /auth/stripe/oauth
GET  /auth/stripe/callback
POST /api/surveys
PUT  /api/surveys/:id
GET  /api/surveys/:id
```

### Webhook Processing
```
POST /webhooks/:uid
POST /api/survey/respond/:token
GET  /api/tracking/pixel/:token
POST /api/tracking/hover/:token/:option
```

### Customer Portal Integration
```
GET  /exit/:uid
GET  /api/redirect-url
```

### Dashboard & Analytics
```
GET  /api/events
GET  /api/events/export
GET  /api/usage
GET  /api/upgrade/checkout
```

## Webhook Processing Flow

### 1. Event Reception
```javascript
// POST /webhooks/:uid
async function handleStripeWebhook(req, res) {
  const { uid } = req.params;
  const event = req.body;
  
  // Verify webhook signature
  const signature = req.headers['stripe-signature'];
  const isValid = stripe.webhooks.constructEvent(
    req.body, signature, process.env.STRIPE_WEBHOOK_SECRET
  );
  
  if (!isValid) return res.status(400).send('Invalid signature');
  
  // Process subscription.canceled events
  if (event.type === 'customer.subscription.deleted') {
    await processCancellation(event, uid);
  }
  
  res.status(200).send('OK');
}
```

### 2. Cancellation Processing
```javascript
async function processCancellation(event, uid) {
  const user = await getUserByUid(uid);
  const subscription = event.data.object;
  
  // Check usage limits
  const usage = await getCurrentUsage(user.id);
  if (usage.events_count >= getPlanLimit(user.plan_type)) {
    return; // Silently ignore if over limit
  }
  
  // Create event record
  const eventRecord = await createEvent({
    user_id: user.id,
    stripe_event_id: event.id,
    customer_id: subscription.customer,
    subscription_id: subscription.id,
    plan_name: subscription.items.data[0].price.nickname,
    mrr_lost: calculateMRR(subscription),
    canceled_at: new Date(subscription.canceled_at * 1000)
  });
  
  // Generate survey token and send email
  await sendSurveyEmail(eventRecord, user);
  
  // Update usage counter
  await incrementUsage(user.id);
}
```

## Email Delivery System

### Survey Email Generation
```javascript
async function sendSurveyEmail(event, user) {
  const survey = await getActiveSurvey(user.id);
  const token = generateOneTimeToken();
  
  // Store response record
  await createResponse({
    event_id: event.id,
    survey_id: survey.id,
    token: token,
    selected_option: null,
    responded_at: null
  });
  
  // Send email via SendGrid
  const emailData = {
    to: event.customer_email,
    from: survey.branding.from_email,
    subject: `One quick question about ${survey.branding.company_name}`,
    template: 'survey-email',
    templateData: {
      customer_name: event.customer_name,
      plan_name: event.plan_name,
      survey_url: `${process.env.SURVEY_BASE_URL}/r/${token}`,
      options: survey.options,
      branding: survey.branding,
      tracking_pixel: survey.tracking_enabled ? 
        `${process.env.API_BASE_URL}/api/tracking/pixel/${token}` : null
    }
  };
  
  await sendGrid.send(emailData);
}
```

## Customer Portal Redirect System

### Redirect URL Generation
```javascript
// Generate unique redirect URL for each user
async function generateCustomerPortalUrl(userId) {
  const user = await getUserById(userId);
  const uid = user.stripe_account_id || user.id;
  
  return `${process.env.SURVEY_BASE_URL}/exit/${uid}`;
}

// Handle direct survey access via Customer Portal
async function handleCustomerPortalSurvey(req, res) {
  const { uid } = req.params;
  
  // Find user by UID
  const user = await getUserByUid(uid);
  if (!user) {
    return res.status(404).send('Invalid survey link');
  }
  
  // Get active survey configuration
  const survey = await getActiveSurvey(user.id);
  if (!survey) {
    return res.status(404).send('No active survey found');
  }
  
  // Render branded survey page
  res.render('survey-page', {
    survey: survey,
    uid: uid,
    isCustomerPortal: true
  });
}
```

### Onboarding Integration
```javascript
// Step 3 of onboarding wizard - Customer Portal setup
async function handleOnboardingStep3(req, res) {
  const userId = req.user.id;
  const redirectUrl = await generateCustomerPortalUrl(userId);
  
  res.render('onboarding-step3', {
    redirectUrl: redirectUrl,
    setupInstructions: {
      step1: 'Go to Stripe Dashboard → Customer Portal → Settings',
      step2: 'Find "After cancellation, redirect to" field',
      step3: `Paste this URL: ${redirectUrl}`,
      step4: 'Save settings'
    }
  });
}

// API endpoint to get redirect URL for user
async function getRedirectUrl(req, res) {
  const userId = req.user.id;
  const redirectUrl = await generateCustomerPortalUrl(userId);
  
  res.json({
    redirectUrl: redirectUrl,
    setupInstructions: [
      'Stripe Dashboard → Customer Portal → Settings',
      'Paste URL in "After cancellation, redirect to" field',
      'Save settings'
    ]
  });
}
```

## Survey Response Processing

### Response Collection
```javascript
// POST /api/survey/respond/:token
async function handleSurveyResponse(req, res) {
  const { token } = req.params;
  const { selected_option, custom_text } = req.body;
  
  // Find and validate response record
  const response = await getResponseByToken(token);
  if (!response || response.responded_at) {
    return res.status(404).send('Survey already completed or invalid');
  }
  
  // Update response
  await updateResponse(token, {
    selected_option,
    custom_text,
    responded_at: new Date(),
    status: 'responded'
  });
  
  // Enrich with event data and send to Slack
  await sendSlackNotification(response);
  
  res.json({ message: 'Thanks!' });
}
```

### Non-Response Data Handling

The system treats non-response as valuable data through three mechanisms:

#### 1. Soft Follow-up System
```javascript
// Scheduled job for follow-up emails
async function processFollowUpEmails() {
  const pendingResponses = await getPendingResponses(72); // 72 hours old
  
  for (const response of pendingResponses) {
    const survey = await getSurveyById(response.survey_id);
    
    // Check if follow-up is enabled
    if (!survey.follow_up_enabled) {
      await markAsNoResponse(response.id);
      continue;
    }
    
    // Send follow-up email
    await sendFollowUpEmail(response, survey);
    
    // Schedule final check in 24 hours
    await scheduleFinalCheck(response.id, 24);
  }
}

async function sendFollowUpEmail(response, survey) {
  const event = await getEventById(response.event_id);
  
  const emailData = {
    to: event.customer_email,
    from: survey.branding.from_email,
    subject: `Quick reminder: ${survey.branding.company_name}`,
    template: 'follow-up-email',
    templateData: {
      customer_name: event.customer_name,
      survey_url: `${process.env.SURVEY_BASE_URL}/r/${response.token}`,
      options: survey.options,
      branding: survey.branding
    }
  };
  
  await sendGrid.send(emailData);
  await updateResponse(response.id, { follow_up_sent_at: new Date() });
}
```

#### 2. Auto-Tagging System
```javascript
// Mark responses as no-response after follow-up period
async function markAsNoResponse(responseId) {
  await updateResponse(responseId, {
    status: 'no_response',
    no_response_at: new Date()
  });
  
  // Send Slack notification for no-response
  const response = await getResponseById(responseId);
  await sendNoResponseSlackNotification(response);
}

async function sendNoResponseSlackNotification(response) {
  const event = await getEventById(response.event_id);
  const survey = await getSurveyById(response.survey_id);
  
  const message = {
    channel: survey.slack_channel_id,
    text: `❓ Churn – no response – ${event.customer_name} – $${event.mrr_lost} MRR`,
    attachments: [{
      fields: [
        { title: 'Customer', value: event.customer_email, short: true },
        { title: 'Plan', value: event.plan_name, short: true },
        { title: 'Status', value: 'No response', short: true }
      ]
    }]
  };
  
  await slack.postMessage(message);
}
```

#### 3. Passive Signal Capture
```javascript
// Email tracking pixel endpoint
async function handleTrackingPixel(req, res) {
  const { token } = req.params;
  
  // Record email open
  await recordEmailOpen(token);
  
  // Return 1x1 transparent pixel
  const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');
  res.writeHead(200, {
    'Content-Type': 'image/gif',
    'Content-Length': pixel.length,
    'Cache-Control': 'no-cache'
  });
  res.end(pixel);
}

// Click tracking for hover heat-map (Scale plan)
async function handleClickTracking(req, res) {
  const { token, option } = req.params;
  
  // Record hover/click on option
  await recordOptionHover(token, option);
  
  res.status(200).send('OK');
}

async function recordEmailOpen(token) {
  const response = await getResponseByToken(token);
  if (response) {
    await updateResponse(response.id, {
      email_opened_at: new Date(),
      open_count: (response.open_count || 0) + 1
    });
  }
}

async function recordOptionHover(token, option) {
  const response = await getResponseByToken(token);
  if (response) {
    const hoverData = response.hover_data || {};
    hoverData[option] = (hoverData[option] || 0) + 1;
    
    await updateResponse(response.id, {
      hover_data: hoverData
    });
  }
}
```

## User Management

### UID Generation for Customer Portal
```javascript
// Generate unique UID for Customer Portal redirects
async function generateUid() {
  const uid = crypto.randomBytes(16).toString('hex');
  
  // Check for uniqueness
  const existing = await db.query(
    'SELECT id FROM users WHERE uid = $1',
    [uid]
  );
  
  if (existing.rows.length > 0) {
    return generateUid(); // Recursive call if collision
  }
  
  return uid;
}

// Create user with UID
async function createUser(userData) {
  const uid = await generateUid();
  
  const user = await db.query(`
    INSERT INTO users (uid, stripe_customer_id, stripe_account_id, email, plan_type)
    VALUES ($1, $2, $3, $4, $5)
    RETURNING *
  `, [uid, userData.stripe_customer_id, userData.stripe_account_id, userData.email, 'free']);
  
  return user.rows[0];
}

// Get user by UID for Customer Portal redirects
async function getUserByUid(uid) {
  const result = await db.query(
    'SELECT * FROM users WHERE uid = $1',
    [uid]
  );
  
  return result.rows[0] || null;
}
```

## Slack Integration

### Notification Formatting
```javascript
async function sendSlackNotification(response) {
  const event = await getEventById(response.event_id);
  const survey = await getSurveyById(response.survey_id);
  const user = await getUserById(survey.user_id);
  
  // Emoji coding based on response
  const emojiMap = {
    'too_expensive': '🔴',
    'missing_feature': '🟡',
    'switching_tool': '🟠',
    'no_longer_needed': '🟢',
    'something_else': '⚪'
  };
  
  const emoji = emojiMap[response.selected_option] || '❓';
  
  const message = {
    channel: survey.slack_channel_id,
    text: `${emoji} Churn detected: ${event.customer_name} – $${event.mrr_lost} MRR – '${response.selected_option}'`,
    attachments: [{
      fields: [
        { title: 'Customer', value: event.customer_email, short: true },
        { title: 'Plan', value: event.plan_name, short: true },
        { title: 'Canceled', value: formatDate(event.canceled_at), short: true },
        { title: 'Reason', value: response.selected_option, short: true }
      ],
      actions: [{
        type: 'button',
        text: 'View Details',
        url: `${process.env.DASHBOARD_URL}/events/${event.id}`
      }]
    }]
  };
  
  await slack.postMessage(message);
}

// Enhanced notification for no-response with tracking data
async function sendNoResponseSlackNotification(response) {
  const event = await getEventById(response.event_id);
  const survey = await getSurveyById(response.survey_id);
  
  let statusText = 'No response';
  if (response.email_opened_at) {
    statusText = `No response – email opened ${response.open_count}×`;
  }
  
  const message = {
    channel: survey.slack_channel_id,
    text: `❓ Churn – ${statusText} – ${event.customer_name} – $${event.mrr_lost} MRR`,
    attachments: [{
      fields: [
        { title: 'Customer', value: event.customer_email, short: true },
        { title: 'Plan', value: event.plan_name, short: true },
        { title: 'Status', value: statusText, short: true }
      ],
      actions: [{
        type: 'button',
        text: 'View Details',
        url: `${process.env.DASHBOARD_URL}/events/${event.id}`
      }]
    }]
  };
  
  await slack.postMessage(message);
}
```

## Scheduled Jobs & Background Processing

### Follow-up Email Processing
```javascript
// Cron job: Every hour, check for responses needing follow-up
async function scheduledFollowUpCheck() {
  const threeDaysAgo = new Date(Date.now() - 72 * 60 * 60 * 1000);
  
  const pendingResponses = await db.query(`
    SELECT r.*, s.follow_up_enabled 
    FROM responses r
    JOIN surveys s ON r.survey_id = s.id
    WHERE r.status = 'pending' 
    AND r.created_at < $1
    AND r.follow_up_sent_at IS NULL
  `, [threeDaysAgo]);
  
  for (const response of pendingResponses.rows) {
    if (response.follow_up_enabled) {
      await sendFollowUpEmail(response);
    } else {
      await markAsNoResponse(response.id);
    }
  }
}

// Cron job: Every hour, mark final no-responses
async function scheduledFinalCheck() {
  const fourDaysAgo = new Date(Date.now() - 96 * 60 * 60 * 1000);
  
  const pendingResponses = await db.query(`
    SELECT r.* 
    FROM responses r
    WHERE r.status = 'pending' 
    AND r.created_at < $1
    AND (r.follow_up_sent_at IS NULL OR r.follow_up_sent_at < $2)
  `, [fourDaysAgo, new Date(Date.now() - 24 * 60 * 60 * 1000)]);
  
  for (const response of pendingResponses.rows) {
    await markAsNoResponse(response.id);
  }
}
```

## Usage Tracking & Limits

### Quota Management
```javascript
async function checkUsageLimits(userId) {
  const user = await getUserById(userId);
  const currentMonth = format(new Date(), 'yyyy-MM');
  
  let usage = await getUsageLog(userId, currentMonth);
  if (!usage) {
    usage = await createUsageLog(userId, currentMonth);
  }
  
  const limits = {
    free: 25,
    pro: 500,
    scale: Infinity
  };
  
  return {
    current: usage.events_count,
    limit: limits[user.plan_type],
    percentage: (usage.events_count / limits[user.plan_type]) * 100
  };
}
```

## Upgrade Flow

### Stripe Checkout Integration
```javascript
async function createUpgradeCheckout(userId, planType) {
  const user = await getUserById(userId);
  
  const session = await stripe.checkout.sessions.create({
    customer: user.stripe_customer_id,
    payment_method_types: ['card'],
    line_items: [{
      price: getPlanPriceId(planType),
      quantity: 1,
    }],
    mode: 'subscription',
    success_url: `${process.env.DASHBOARD_URL}/upgrade/success?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${process.env.DASHBOARD_URL}/upgrade/cancel`,
    metadata: {
      user_id: userId,
      plan_type: planType
    }
  });
  
  return session;
}
```

## Security Considerations

### Webhook Security
- Stripe signature verification on all webhook requests
- Rate limiting on webhook endpoints
- Event deduplication using Stripe event IDs

### Token Security
- One-time use tokens for survey responses
- Token expiration (24 hours)
- Secure token generation using crypto.randomBytes

### Data Protection
- PII encryption for customer data
- GDPR compliance for EU customers
- Data retention policies

## Performance Optimizations

### Database Indexing
```sql
-- Critical indexes for performance
CREATE INDEX idx_events_user_id ON events(user_id);
CREATE INDEX idx_events_canceled_at ON events(canceled_at);
CREATE INDEX idx_responses_token ON responses(token);
CREATE INDEX idx_usage_user_month ON usage_logs(user_id, month_year);
CREATE INDEX idx_users_uid ON users(uid);
```

### Caching Strategy
- Redis caching for user plan information
- Survey template caching
- Slack channel information caching

### Queue Processing
- Background job processing for email sending
- Async Slack notification processing
- Batch processing for usage calculations
- Scheduled follow-up email processing (72-hour delay)
- Final no-response marking (96-hour delay)

## Monitoring & Observability

### Key Metrics
- Webhook processing latency
- Email delivery success rate
- Survey response rate
- Slack notification delivery rate
- Database query performance

### Error Handling
- Comprehensive error logging
- Retry mechanisms for failed webhooks
- Dead letter queues for failed emails
- Slack notification fallbacks

## Deployment Strategy

### Environment Configuration
```bash
# Required environment variables
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
SENDGRID_API_KEY=
SLACK_BOT_TOKEN=
DATABASE_URL=
REDIS_URL=
SURVEY_BASE_URL=
DASHBOARD_URL=
API_BASE_URL=
```

### Infrastructure
- **Database**: Managed PostgreSQL (Fly.io/Railway)
- **Cache**: Redis for session and job queues
- **CDN**: For static assets and email templates
- **Monitoring**: Application performance monitoring

## Development Roadmap

### Phase 1: MVP (Week 1-2)
- [x] Basic webhook processing
- [x] Email survey delivery
- [x] Slack notifications
- [x] User authentication
- [x] Customer Portal redirect system

### Phase 2: Enhancement (Week 3-4)
- [ ] Dashboard with analytics
- [ ] CSV export functionality
- [ ] Advanced survey templates
- [ ] Usage tracking and limits

### Phase 3: Scale Features (Week 5-6)
- [ ] Win-back email sequences
- [ ] Advanced analytics
- [ ] API rate limiting
- [ ] Multi-tenant optimizations

## Conclusion

The Churn-Exit Survey Bot backend is designed for simplicity, reliability, and scalability. The system prioritizes real-time processing, secure data handling, and seamless integration with Stripe and Slack ecosystems. The architecture supports the core value proposition of providing immediate churn insights with minimal setup friction. 