Churn-Exit Survey Bot  
Single-purpose SaaS for Stripe-based startups: “When a subscription is cancelled, ask the customer why—in one click.”

1. Core job  
   Detect Stripe subscription.canceled → send one-question survey → push response to Slack.

2. Day-0 user flow (under 60 seconds)  
   a. Sign in with Stripe (OAuth).  
   b. Pick survey style:  
      • Single multiple-choice (default)  
      • Single-choice + optional text box  
   c. Choose Slack channel.  
   d. Toggle on. Done. No code, no forms.

3. Survey delivery mechanics  
   • Via email (SendGrid).  
   • From user’s own domain (SPF/DKIM auto-setup).  
   • Link in email is a unique 1-click tokenized URL; no login friction.  
   • Mobile-first micro-form (<15 s to answer).

4. Data flow  
   • Response hits our API.  
   • We enrich with Stripe plan name, MRR lost, cancellation date.  
   • JSON payload posted to chosen Slack channel with emoji color-coding:  
     🔴 “too expensive”  
     🟡 “missing feature”  
     🟢 “stakeholder left” etc.  
   • Optional: append to a Google Sheet or push to Segment.

5. Pricing model  
   • Free: up to 25 churn events / month.  
   • Pro: $19 / month for 500 events, white-label email.  
   • Scale: $49 / month unlimited + Webhook out + CSV export.

6. Tech stack (MVP)  
   • Next.js + Tailwind landing page.  
   • Node.js worker listening to Stripe webhooks.  
   • Postgres table: events, surveys, responses.  
   • SendGrid for email, Slack OAuth for channel selection.  
   • Deploy on Fly.io or Railway → live in <1 week.

7. Growth loop  
   • Footer in survey email: “Powered by Churn-Exit Survey Bot – get it for your SaaS.”  
   • One-click referral link gives both referrer and referee +100 free events.

8. Expansion revenue (still single-purpose)  
   • Add “win-back email sequence” toggle: if reason = “too expensive”, auto-send 10 %-off coupon after 24 h.  
   • Add “reason cohorts dashboard” (still only about the exit survey).

9. Positioning tagline  
   “Find out why customers leave your Stripe app—in one click.”