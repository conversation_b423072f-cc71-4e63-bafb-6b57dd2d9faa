# Churn-Exit Survey Bot - Unified API Specification

## Overview

This document provides the authoritative API specification for the Churn-Exit Survey Bot SaaS application. It reconciles the existing implementation with planned features and clearly marks what's implemented vs what needs development.

**Legend:**
- ✅ **Implemented** - Currently working in the backend
- 🚧 **Partially Implemented** - Basic structure exists, needs refinement
- ❌ **Not Implemented** - Needs to be built
- 🔄 **Needs Reconciliation** - Different implementations exist

---

## Base URL
```
Development: http://localhost:4000
Production: https://api.yourdomain.com
```

## Authentication

### JWT Token Format
```
Authorization: Bearer <jwt_token>
```

### Token Structure
```json
{
  "userId": "user-uuid",
  "email": "<EMAIL>",
  "planType": "free|pro|scale",
  "iat": **********,
  "exp": **********
}
```

---

## 🔐 Authentication Endpoints

### OAuth Flows

#### Stripe OAuth
```http
GET /api/auth/stripe/oauth
```
**Response:**
```json
{
  "success": true,
  "oauthUrl": "https://connect.stripe.com/oauth/authorize?...",
  "message": "Redirect user to this URL to connect Stripe account"
}
```

#### Stripe OAuth Callback
```http
GET /api/auth/stripe/oauth/callback
```
**Query Parameters:**
- `code` (required): Authorization code from Stripe
- `state` (optional): State parameter for security

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "plan_type": "free"
  },
  "token": "jwt_token_here",
  "message": "Stripe account connected successfully"
}
```

#### Slack OAuth
```http
GET /api/auth/slack/oauth
```
**Response:**
```json
{
  "success": true,
  "oauthUrl": "https://slack.com/oauth/v2/authorize?...",
  "message": "Redirect user to this URL to connect Slack workspace"
}
```

#### Slack OAuth Callback
```http
GET /api/auth/slack/oauth/callback
```
**Query Parameters:**
- `code` (required): Authorization code from Slack
- `state` (required): User ID or correlation key

**Response:**
```json
{
  "success": true,
  "workspace": {
    "id": "T**********",
    "name": "Workspace Name"
  },
  "message": "Slack workspace connected successfully"
}
```

#### Get Redirect URL
```http
GET /api/auth/redirect-url
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "redirectUrl": "https://yourdomain.com/exit/survey-token",
  "message": "Copy this URL to your Stripe Customer Portal settings"
}
```

---

## 👤 User Management Endpoints

### Get Current User
```http
GET /api/users/me
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "plan_type": "free",
    "stripe_customer_id": "cus_xxx",
    "slack_workspace_id": "T123456",
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

### Get User Usage
```http
GET /api/users/usage
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "usage": {
    "current": 15,
    "limit": 25,
    "percentage": 60
  },
  "upgradePrompt": {
    "show": true,
    "message": "Upgrade to Pro for more events"
  }
}
```

### Get User Events
```http
GET /api/users/events
Authorization: Bearer <token>
```
**Query Parameters:**
- `limit` (optional): Number of events (default: 50)
- `offset` (optional): Offset for pagination (default: 0)

**Response:**
```json
{
  "success": true,
  "events": [
    {
      "id": "event-uuid",
      "stripe_event_id": "evt_xxx",
      "event_type": "customer.subscription.deleted",
      "customer_id": "cus_xxx",
      "mrr_lost": 5000,
      "created_at": "2025-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "limit": 50,
    "offset": 0,
    "hasMore": true
  }
}
```

---

## 📊 Billing & Usage Endpoints

### Get Current Usage (Billing Namespace)
```http
GET /api/billing/usage
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "usage": {
    "current": 15,
    "limit": 25,
    "percentage": 60,
    "planType": "free",
    "canProcess": true
  }
}
```

### Get Plan Limits
```http
GET /api/billing/limits
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "limits": {
    "current": 15,
    "limit": 25,
    "percentage": 60,
    "planType": "free",
    "canProcess": true
  }
}
```

### Plan Features

#### Get Plan Features
```http
GET /api/billing/features/{planType}
```
**Parameters:**
- `planType`: `free`, `pro`, or `scale`

**Response:**
```json
{
  "success": true,
  "planType": "pro",
  "features": [
    "basic_surveys",
    "response_viewer",
    "email_tracking",
    "white_label_email",
    "advanced_filters"
  ]
}
```

#### Check Feature Access
```http
GET /api/billing/features/check/{feature}
Authorization: Bearer <token>
```
**Parameters:**
- `feature`: Feature name to check

**Response:**
```json
{
  "success": true,
  "feature": "white_label_email",
  "hasFeature": false
}
```

### Billing Management

#### Create Checkout Session
```http
POST /api/billing/checkout/{planType}
Authorization: Bearer <token>
```
**Parameters:**
- `planType`: `pro` or `scale`

**Response:**
```json
{
  "success": true,
  "checkoutUrl": "https://checkout.stripe.com/pay/cs_xxx",
  "sessionId": "cs_xxx"
}
```

#### Create Customer Portal Session
```http
POST /api/billing/portal
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "portalUrl": "https://billing.stripe.com/session/ps_xxx",
  "sessionId": "ps_xxx"
}
```

---

## 📋 Survey Management Endpoints

### Survey CRUD Operations

#### Create Survey
```http
POST /api/surveys
Authorization: Bearer <token>
Content-Type: application/json

{
  "question_text": "Why did you cancel?",
  "options": [
    {"id": "1", "text": "Too expensive", "value": "expensive"},
    {"id": "2", "text": "Missing features", "value": "features"},
    {"id": "3", "text": "Poor support", "value": "support"},
    {"id": "4", "text": "Other", "value": "other"}
  ],
  "branding": {
    "company_name": "Your Company",
    "logo_url": "https://example.com/logo.png",
    "primary_color": "#007bff",
    "from_name": "Your Team",
    "from_email": "<EMAIL>"
  },
  "slack_channel_id": "C**********",
  "follow_up_enabled": true,
  "tracking_enabled": true
}
```
**Response:**
```json
{
  "success": true,
  "survey": {
    "id": "survey-uuid",
    "user_id": "user-uuid",
    "question_text": "Why did you cancel?",
    "options": [...],
    "branding": {...},
    "is_active": true,
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

#### Get Active Survey
```http
GET /api/surveys/active
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "survey": {
    "id": "survey-uuid",
    "question_text": "Why did you cancel?",
    "options": [...],
    "branding": {...},
    "is_active": true,
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

#### Get User Surveys
```http
GET /api/surveys/user/{userId}
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "surveys": [
    {
      "id": "survey-uuid",
      "question_text": "Why did you cancel?",
      "options": [...],
      "is_active": true,
      "created_at": "2025-01-01T00:00:00Z"
    }
  ]
}
```

#### Get Survey by ID
```http
GET /api/surveys/{surveyId}
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "survey": {
    "id": "survey-uuid",
    "question_text": "Why did you cancel?",
    "options": [...],
    "branding": {...},
    "is_active": true,
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

#### Update Survey
```http
PUT /api/surveys/{surveyId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "question_text": "Updated question",
  "options": [...],
  "branding": {...}
}
```

#### Delete Survey
```http
DELETE /api/surveys/{surveyId}
Authorization: Bearer <token>
```

### Survey Responses

#### Get Survey Responses
```http
GET /api/surveys/{surveyId}/responses
Authorization: Bearer <token>
```
**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 50)
- `status`: Filter by status (`pending`, `responded`, `no_response`)

**Response:**
```json
{
  "success": true,
  "responses": [
    {
      "id": "response-uuid",
      "selected_option": "1",
      "custom_text": "Additional feedback",
      "status": "responded",
      "customer_email": "<EMAIL>",
      "customer_name": "John Customer",
      "created_at": "2025-01-01T00:00:00Z",
      "responded_at": "2025-01-01T00:05:00Z"
    }
  ]
}
```

#### Generate Survey URL
```http
POST /api/surveys/{surveyId}/generate-url
Authorization: Bearer <token>
Content-Type: application/json

{
  "customerEmail": "<EMAIL>",
  "customerName": "John Customer",
  "stripeCustomerId": "cus_xxx"
}
```
**Response:**
```json
{
  "success": true,
  "surveyUrl": "https://yourdomain.com/exit/survey-token-here"
}
```

#### Send Survey Email
```http
POST /api/surveys/{surveyId}/send-email
Authorization: Bearer <token>
Content-Type: application/json

{
  "customerEmail": "<EMAIL>",
  "customerName": "John Customer",
  "stripeCustomerId": "cus_xxx"
}
```
**Response:**
```json
{
  "success": true,
  "emailId": "sg_xxx",
  "message": "Email sent successfully"
}
```

---

## 📤 Export Endpoints

### CSV Export (User Namespace)
```http
GET /api/users/events/export
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "message": "CSV export endpoint (Pro plan only)"
}
```

### CSV Export (Billing Namespace - Scale Plan Only)

#### Export Survey Responses
```http
GET /api/export/survey/{surveyId}/csv
Authorization: Bearer <token>
```
**Query Parameters:**
- `startDate`: Start date filter (ISO format)
- `endDate`: End date filter (ISO format)
- `status`: Filter by status

**Response:** CSV file download

#### Export Usage Data
```http
GET /api/export/usage/csv
Authorization: Bearer <token>
```
**Query Parameters:**
- `startMonth`: Start month (YYYY-MM format)
- `endMonth`: End month (YYYY-MM format)

**Response:** CSV file download

---

## 🔗 Webhook Endpoints

### Stripe Webhooks (Per-User)
```http
POST /webhooks/{uid}
Content-Type: application/json
Stripe-Signature: whsec_xxx

{
  "type": "customer.subscription.deleted",
  "data": {
    "object": {
      "id": "sub_xxx",
      "customer": "cus_xxx",
      "metadata": {
        "userId": "user-uuid"
      }
    }
  }
}
```
**Response:**
```json
{
  "received": true
}
```

### Stripe Webhooks (Global)
```http
POST /webhooks/stripe
Content-Type: application/json
Stripe-Signature: whsec_xxx

{
  "type": "customer.subscription.deleted",
  "data": {
    "object": {
      "id": "sub_xxx",
      "customer": "cus_xxx",
      "metadata": {
        "userId": "user-uuid"
      }
    }
  }
}
```

### Survey Response Webhook
```http
POST /webhooks/survey/respond/{token}
Content-Type: application/json

{
  "selected_option": "1",
  "custom_text": "Additional feedback"
}
```
**Response:**
```json
{
  "success": true,
  "message": "Thanks!"
}
```

---

## 📈 Tracking Endpoints

### Email Tracking (API Namespace)
```http
GET /api/tracking/pixel/{token}
```
**Response:** 1x1 transparent GIF image
**Side Effects:**
- Updates `response.email_opened_at = now`
- Increments `response.open_count`

### Hover Tracking (API Namespace)
```http
POST /api/tracking/hover/{token}/{option}
```
**Response:**
```json
{
  "success": true
}
```
**Side Effects:**
- Records hover/click heat-map analytics
- Increments per-option counter

### Email Tracking (Webhook Namespace)
```http
GET /webhooks/tracking/pixel/{token}
```
**Response:** 1x1 transparent GIF image
**Side Effects:** Same as API namespace

### Hover Tracking (Webhook Namespace)
```http
POST /webhooks/tracking/hover/{token}/{option}
```
**Response:**
```json
{
  "success": true
}
```
**Side Effects:** Same as API namespace

### Email Tracking (Billing Namespace)
```http
GET /api/tracking/email/{responseId}
```
**Response:**
```json
{
  "success": true,
  "tracking": {
    "email_sent_at": "2025-01-01T00:00:00Z",
    "email_opened_at": "2025-01-01T00:02:00Z",
    "link_clicked_at": "2025-01-01T00:03:00Z"
  }
}
```

---

## 🏥 Health & Monitoring

### Health Check
```http
GET /health
```
**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-01T00:00:00Z",
  "services": {
    "database": "connected",
    "redis": "connected",
    "jobProcessor": "running"
  }
}
```

### Metrics
```http
GET /metrics
```
**Response:**
```json
{
  "uptime": 3600,
  "memory": {
    "used": 51200000,
    "total": **********
  },
  "requests": {
    "total": 1000,
    "successful": 950,
    "failed": 50
  }
}
```

---

## 🚨 Error Responses

### Standard Error Format
```json
{
  "error": "Error message",
  "message": "Detailed error description",
  "code": "ERROR_CODE"
}
```

### Common Error Codes
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient plan level)
- `404`: Not found
- `429`: Rate limit exceeded
- `500`: Internal server error

### Plan Limit Errors
```json
{
  "error": "Usage limit exceeded",
  "message": "You have reached your monthly limit of 25 events",
  "currentPlan": "free",
  "requiredPlan": "pro",
  "currentUsage": 25,
  "limit": 25
}
```

---

## 📝 Implementation Status

### ✅ Fully Implemented
- Authentication endpoints (`/api/auth/*`)
- User management (`/api/users/me`)
- Survey CRUD operations (`/api/surveys/*`)
- Billing usage tracking (`/api/billing/usage`)
- Health and metrics endpoints
- Webhook processing (basic structure)

### 🚧 Partially Implemented
- Survey responses (needs refinement)
- Export functionality (basic structure exists)
- Tracking endpoints (needs consolidation)

### ❌ Not Implemented
- Advanced filtering for survey responses
- Team sharing features
- Webhook outbound for Scale plan
- Advanced analytics dashboard endpoints

### 🔄 Needs Reconciliation
- **User vs Auth namespace**: `/api/users/me` vs `/api/auth/me`
- **Usage paths**: `/api/users/usage` vs `/api/billing/usage`
- **Webhook patterns**: Per-user vs global webhooks
- **Tracking endpoints**: Multiple implementations exist

---

## 🔧 Development Notes

### Environment Variables
```bash
# Required for all features
JWT_SECRET=your-secret-key
DATABASE_URL=postgresql://...
REDIS_URL=redis://...

# Stripe integration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_CLIENT_ID=ca_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email service
SENDGRID_API_KEY=SG...

# Slack integration
SLACK_BOT_TOKEN=xoxb-...
SLACK_CLIENT_ID=...
SLACK_CLIENT_SECRET=...
```

### Rate Limiting
- **Authentication**: 100 requests per 15 minutes
- **Webhooks**: 300 requests per 15 minutes
- **General API**: 1000 requests per 15 minutes

### CORS Configuration
In production, allowed origins are limited to:
- `dashboardUrl` from config
- `surveyBaseUrl` from config

---

This unified specification provides a clear roadmap for implementing the complete API surface area for the Churn-Exit Survey Bot SaaS application. 