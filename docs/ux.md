Churn-Exit Survey Bot  
End-to-end onboarding & upgrade experience (Stripe-only SaaS)

-------------------------------------------------
1. Discovery → Landing
   • URL: churnbot.dev
   • Hero: “Find out why customers cancel in one click.”
   • Single CTA: “Connect with Stripe – 60-second setup.”

2. OAuth Connect (30 s)
   • User clicks → Stripe OAuth popup → scopes: `read_only` + `webhooks`.
   • On success, redirected to `/welcome`.

3. 3-Step Wizard (total < 30 s)
   Step 1  Pick survey question  
            • Pre-built template (radio buttons)  
            • OR custom text input.

   Step 2  Branding  
            • Upload logo (auto-resized 120×40 px)  
            • Pick primary color (color-picker)  
            • From-name & from-address (defaults to `founders@domain`).

   Step 3  Slack destination  
            • OAuth to Slack workspace → choose channel  
            • Skip → responses go to email only.

4. Webhook auto-install
   • Backend calls `stripe.webhookEndpoints.create` with the user’s unique URL `https://api.churnbot.dev/webhooks/[uid]`.
   • User sees green checkmark: “Live – we’re listening.”

5. First churn event (usually within hours)
   • When a subscription is canceled, email fires instantly.  
   • Slack message appears:  
     🔔 “Churn detected: Acme Inc – $79 MRR – ‘missing feature’”  
   • User clicks link → dashboard with full response.

6. Dashboard (free tier)
   • Table: customer, plan, MRR lost, reason, timestamp.  
   • Filter by date range, reason, plan.  
   • CSV export disabled (upgrade nudge).

7. In-app Upgrade Prompts
   A. Banner at 20/25 free events:  
      “You’ve used 80 % of this month’s free quota. Upgrade to keep insights flowing.”

   B. “Unlock win-back sequences” toggle (greyed-out) → tooltip:  
      “Pro plan only – send automatic discount emails to price-sensitive churners.”

   C. CSV export button → modal:  
      “Export historical data – Pro plan $19/mo.”

8. Checkout Flow (Stripe Checkout)
   • One click → Checkout page pre-filled with user’s Stripe email.  
   • After payment → webhook upgrades account `plan: 'pro'`, resets quota, unlocks toggles.  
   • User stays inside dashboard; no new login.

9. Post-purchase delight
   • Confetti overlay + Slack DM from bot: “🚀 You’re now on Pro. Win-back sequences are live—flip the toggle above.”

10. Expansion / Scale plan
    • At 450/500 events, banner:  
      “You’re approaching Pro limits. Scale plan ($49 unlimited) keeps you covered.”  
    • One-click upgrade (Stripe Customer Portal) → prorated charge.

11. Cancellation & Off-boarding
    • Customer Portal “Cancel plan.”  
    • Downgrades at period end; dashboard keeps read-only access to historic data.  
    • Optional exit survey (dog-food) → “What made you downgrade?”

Key UX principles
   • Zero code, zero DNS, zero forms after OAuth.  
   • Every friction point removed (<60 s to value).  
   • Upgrade prompts appear contextually right when the limit is felt.