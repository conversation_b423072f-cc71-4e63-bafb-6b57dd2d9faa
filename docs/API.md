# API Documentation

Base URL prefixes
- API routes: /api
- Webhooks: /webhooks

Auth
- GET /api/auth/stripe/oauth
  Description: Generate Stripe Connect OAuth URL for a client to redirect the user.
  Query: state (optional)
  Response:
  {
    success: true,
    oauthUrl: "https://connect.stripe.com/oauth/authorize?...",
    message: "Redirect user to this URL to connect Stripe account"
  }

- GET /api/auth/stripe/oauth/callback
  Description: Stripe OAuth callback; exchanges code, fetches account, creates/updates user, returns session token.
  Query: code (required), state (optional)
  Responses:
  200:
  {
    success: true,
    user: { id: string, email: string, plan_type: "free" | "pro" | "scale" },
    token: string,
    message: "Stripe account connected successfully"
  }
  400: { success: false, error: "Authorization code is required" | "Failed to exchange code for token" }
  500: { success: false, error: "OAuth callback failed" }

- GET /api/auth/slack/oauth
  Description: Generate Slack OAuth URL for connecting a workspace.
  Query: state (optional)
  Response:
  {
    success: true,
    oauthUrl: "https://slack.com/oauth/v2/authorize?...",
    message: "Redirect user to this URL to connect Slack workspace"
  }

- GET /api/auth/slack/oauth/callback
  Description: Slack OAuth callback; exchanges code, updates a user identified by state, returns workspace info.
  Query: code (required), state (required: user id or correlation key)
  Responses:
  200:
  {
    success: true,
    workspace: { id: string, name: string },
    message: "Slack workspace connected successfully"
  }
  400: { success: false, error: "Authorization code is required" | "Failed to exchange code for token" }
  500: { success: false, error: "OAuth callback failed" }

- GET /api/auth/redirect-url
  Auth: Requires req.user to be set by upstream auth middleware.
  Description: Returns the customer-portal redirect URL that points to the survey exit endpoint.
  Response:
  {
    success: true,
    redirectUrl: string,
    message: "Copy this URL to your Stripe Customer Portal settings"
  }

Users
- GET /api/users/me
  Auth: Requires req.user.
  Description: Get current user object.
  Responses:
  200: User
  401: { error: "Authentication required" }
  404: { error: "User not found" }

- GET /api/users/usage
  Auth: Requires req.user.
  Description: Retrieve current usage metrics and potential upgrade prompt.
  Response:
  {
    usage: { current: number, limit: number, percentage: number },
    upgradePrompt: any
  }
  Errors: 401, 500

- GET /api/users/events
  Auth: Requires req.user.
  Query: limit? number (default 50), offset? number (default 0)
  Description: Paginated list of events for the authenticated user.
  Response:
  {
    events: Event[],
    pagination: { limit: number, offset: number, hasMore: boolean }
  }
  Errors: 401, 500

- GET /api/users/events/export
  Auth: Requires req.user.
  Description: CSV export endpoint (Pro and above).
  Responses:
  200: { message: "CSV export endpoint (Pro plan only)" }
  401: { error: "Authentication required" }
  403: { error: "CSV export requires Pro plan" }

Surveys
- POST /api/surveys
  Description: Create a survey.
  Body:
  {
    question_text: string,
    options: { id: string, text: string, value: string }[],
    branding: {
      company_name: string,
      logo_url?: string,
      primary_color: string,
      from_name: string,
      from_email: string
    },
    slack_channel_id: string,
    follow_up_enabled?: boolean,
    tracking_enabled?: boolean
  }
  Notes: Current implementation uses a placeholder user and workspace id; expect replacement once auth is fully wired.
  Responses:
  201: Survey
  400: { error: "Missing required fields" }
  500: { error: "Failed to create survey" }

- GET /api/surveys/active
  Auth: Requires req.user.
  Description: Get the active survey for the authenticated user.
  Responses:
  200: Survey
  401: { error: "Authentication required" }
  404: { error: "No active survey found" }
  500: { error: "Failed to get active survey" }

- PUT /api/surveys/:id
  Description: Update a survey by id.
  Body: Partial<Survey> fields
  Responses:
  200: Survey
  500: { error: "Failed to update survey" }

- GET /api/surveys/:id
  Description: Get survey by id (placeholder response currently).
  Response:
  {
    message: "Get survey by ID endpoint",
    id: string
  }

Tracking
- GET /api/tracking/pixel/:token
  Description: Returns a 1x1 transparent GIF and records email opens for the survey response token.
  Response: image/gif; 200 on success, 500 on error
  Side effects:
  - Updates response.email_opened_at = now
  - Increments response.open_count

- POST /api/tracking/hover/:token/:option
  Description: Records hover/click heat-map analytics by incrementing a per-option counter.
  Response:
  200: { success: true }
  500: { error: "Failed to record hover data" }

Webhooks
- POST /webhooks/:uid
  Description: Stripe webhook receiver. Verifies signature and processes customer.subscription.deleted.
  Headers:
  - stripe-signature: string (required)
  Body: Stripe.Event (raw JSON)
  Responses:
  200: { received: true }
  400: { error: "Webhook processing failed" }
  404: { error: "User not found" }
  Notes:
  - On deletion event:
    * Calculates MRR lost
    * Creates internal event record
    * Fetches user's active survey
    * Generates token and creates survey response
    * Sends survey email
    * Increments usage

- POST /webhooks/survey/respond/:token
  Description: Accepts a survey response for a given token.
  Body:
  {
    selected_option: string,
    custom_text?: string
  }
  Responses:
  200: { message: "Thanks!" }
  404: { error: "Survey already completed or invalid" }
  500: { error: "Failed to process survey response" }

- GET /webhooks/tracking/pixel/:token
  Description: Same behavior as /api/tracking/pixel/:token under the /webhooks namespace.

- POST /webhooks/tracking/hover/:token/:option
  Description: Same behavior as /api/tracking/hover/:token/:option under the /webhooks namespace.

Data models (summarized)
- User: { id, uid, email, plan_type, stripe_* fields, slack_* fields, created_at, updated_at }
- Survey: { id, user_id, question_text, options[], branding, slack_channel_id, slack_workspace_id, follow_up_enabled, tracking_enabled, is_active, created_at }
- Event: { id, user_id, stripe_event_id, event_type, customer_id, subscription_id, plan_name, mrr_lost, canceled_at, processed_at, customer_email?, customer_name? }
- Response: { id, event_id, survey_id, token, selected_option?, custom_text?, status, responded_at?, slack_sent_at?, follow_up_sent_at?, no_response_at?, email_opened_at?, open_count, hover_data? }
- UsageLog: { id, user_id, month_year, events_count, created_at }

Notes
- Auth middleware is assumed for endpoints requiring req.user; ensure JWT/session wiring upstream.
- CORS: In production, allowed origins are limited to dashboardUrl and surveyBaseUrl from config.
- Rate limiting applies globally per server config.