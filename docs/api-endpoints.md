# Churn-Exit Survey Bot - API Documentation

## Overview

This document provides comprehensive API endpoints for the Churn-Exit Survey Bot SaaS application. All endpoints return JSON responses and use JWT authentication via the `Authorization: Bearer <token>` header.

## Base URL
```
Development: http://localhost:4000
Production: https://api.yourdomain.com
```

## Authentication

### JWT Token Format
```
Authorization: Bearer <jwt_token>
```

### Token Structure
```json
{
  "userId": "user-uuid",
  "email": "<EMAIL>",
  "planType": "free|pro|scale",
  "iat": 1234567890,
  "exp": 1234567890
}
```

---

## 🔐 Authentication Endpoints

### OAuth Flows

#### Stripe OAuth
```http
GET /api/auth/stripe/oauth
```
**Response:**
```json
{
  "success": true,
  "authUrl": "https://connect.stripe.com/oauth/authorize?..."
}
```

#### Slack OAuth
```http
GET /api/auth/slack/oauth
```
**Response:**
```json
{
  "success": true,
  "authUrl": "https://slack.com/oauth/v2/authorize?..."
}
```

### User Management

#### Get Current User
```http
GET /api/auth/me
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "plan_type": "free",
    "stripe_customer_id": "cus_xxx",
    "slack_workspace_id": "T123456",
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

#### Refresh Token
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "refresh_token_here"
}
```
**Response:**
```json
{
  "success": true,
  "accessToken": "new_access_token",
  "refreshToken": "new_refresh_token"
}
```

#### Logout
```http
POST /api/auth/logout
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

## 📊 Billing & Usage Endpoints

### Usage Tracking

#### Get Current Usage
```http
GET /api/billing/usage
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "usage": {
    "current": 15,
    "limit": 25,
    "percentage": 60,
    "planType": "free",
    "canProcess": true
  }
}
```

#### Get Plan Limits
```http
GET /api/billing/limits
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "limits": {
    "current": 15,
    "limit": 25,
    "percentage": 60,
    "planType": "free",
    "canProcess": true
  }
}
```

### Plan Features

#### Get Plan Features
```http
GET /api/billing/features/{planType}
```
**Parameters:**
- `planType`: `free`, `pro`, or `scale`

**Response:**
```json
{
  "success": true,
  "planType": "pro",
  "features": [
    "basic_surveys",
    "response_viewer",
    "email_tracking",
    "white_label_email",
    "advanced_filters"
  ]
}
```

#### Check Feature Access
```http
GET /api/billing/features/check/{feature}
Authorization: Bearer <token>
```
**Parameters:**
- `feature`: Feature name to check

**Response:**
```json
{
  "success": true,
  "feature": "white_label_email",
  "hasFeature": false
}
```

### Billing Management

#### Create Checkout Session
```http
POST /api/billing/checkout/{planType}
Authorization: Bearer <token>
```
**Parameters:**
- `planType`: `pro` or `scale`

**Response:**
```json
{
  "success": true,
  "checkoutUrl": "https://checkout.stripe.com/pay/cs_xxx",
  "sessionId": "cs_xxx"
}
```

#### Create Customer Portal Session
```http
POST /api/billing/portal
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "portalUrl": "https://billing.stripe.com/session/ps_xxx",
  "sessionId": "ps_xxx"
}
```

---

## 📋 Survey Management Endpoints

### Survey CRUD Operations

#### Create Survey
```http
POST /api/surveys
Authorization: Bearer <token>
Content-Type: application/json

{
  "question_text": "Why did you cancel?",
  "options": [
    {"id": "1", "text": "Too expensive", "emoji": "💰"},
    {"id": "2", "text": "Missing features", "emoji": "🔧"},
    {"id": "3", "text": "Poor support", "emoji": "💬"},
    {"id": "4", "text": "Other", "emoji": "❓"}
  ],
  "branding": {
    "logo": "https://example.com/logo.png",
    "colors": {
      "primary": "#007bff",
      "secondary": "#6c757d"
    }
  },
  "slack_channel_id": "C1234567890",
  "slack_workspace_id": "T1234567890"
}
```
**Response:**
```json
{
  "success": true,
  "survey": {
    "id": "survey-uuid",
    "user_id": "user-uuid",
    "question_text": "Why did you cancel?",
    "options": [...],
    "branding": {...},
    "is_active": true,
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

#### Get User Surveys
```http
GET /api/surveys/user/{userId}
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "surveys": [
    {
      "id": "survey-uuid",
      "question_text": "Why did you cancel?",
      "options": [...],
      "is_active": true,
      "created_at": "2025-01-01T00:00:00Z"
    }
  ]
}
```

#### Get Survey by ID
```http
GET /api/surveys/{surveyId}
Authorization: Bearer <token>
```
**Response:**
```json
{
  "success": true,
  "survey": {
    "id": "survey-uuid",
    "question_text": "Why did you cancel?",
    "options": [...],
    "branding": {...},
    "is_active": true,
    "created_at": "2025-01-01T00:00:00Z"
  }
}
```

#### Update Survey
```http
PUT /api/surveys/{surveyId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "question_text": "Updated question",
  "options": [...],
  "branding": {...}
}
```

#### Delete Survey
```http
DELETE /api/surveys/{surveyId}
Authorization: Bearer <token>
```

### Survey Responses

#### Get Survey Responses
```http
GET /api/surveys/{surveyId}/responses
Authorization: Bearer <token>
```
**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 50)
- `status`: Filter by status (`pending`, `responded`, `no_response`)

**Response:**
```json
{
  "success": true,
  "responses": [
    {
      "id": "response-uuid",
      "selected_option": "1",
      "custom_text": "Additional feedback",
      "status": "responded",
      "customer_email": "<EMAIL>",
      "customer_name": "John Customer",
      "created_at": "2025-01-01T00:00:00Z",
      "responded_at": "2025-01-01T00:05:00Z"
    }
  ]
}
```

#### Generate Survey URL
```http
POST /api/surveys/{surveyId}/generate-url
Authorization: Bearer <token>
Content-Type: application/json

{
  "customerEmail": "<EMAIL>",
  "customerName": "John Customer",
  "stripeCustomerId": "cus_xxx"
}
```
**Response:**
```json
{
  "success": true,
  "surveyUrl": "https://yourdomain.com/exit/survey-token-here"
}
```

#### Send Survey Email
```http
POST /api/surveys/{surveyId}/send-email
Authorization: Bearer <token>
Content-Type: application/json

{
  "customerEmail": "<EMAIL>",
  "customerName": "John Customer",
  "stripeCustomerId": "cus_xxx"
}
```
**Response:**
```json
{
  "success": true,
  "emailId": "sg_xxx",
  "message": "Email sent successfully"
}
```

---

## 📤 Export Endpoints (Scale Plan Only)

### CSV Export

#### Export Survey Responses
```http
GET /api/export/survey/{surveyId}/csv
Authorization: Bearer <token>
```
**Query Parameters:**
- `startDate`: Start date filter (ISO format)
- `endDate`: End date filter (ISO format)
- `status`: Filter by status

**Response:** CSV file download

#### Export Usage Data
```http
GET /api/export/usage/csv
Authorization: Bearer <token>
```
**Query Parameters:**
- `startMonth`: Start month (YYYY-MM format)
- `endMonth`: End month (YYYY-MM format)

**Response:** CSV file download

---

## 🔗 Webhook Endpoints

### Stripe Webhooks
```http
POST /webhooks/stripe
Content-Type: application/json
Stripe-Signature: whsec_xxx

{
  "type": "customer.subscription.deleted",
  "data": {
    "object": {
      "id": "sub_xxx",
      "customer": "cus_xxx",
      "metadata": {
        "userId": "user-uuid"
      }
    }
  }
}
```

### Survey Response Webhook
```http
POST /webhooks/survey-response
Content-Type: application/json

{
  "token": "survey-token",
  "selectedOption": "1",
  "customText": "Additional feedback"
}
```

---

## 📈 Tracking Endpoints

### Email Tracking
```http
GET /api/tracking/email/{responseId}
```
**Response:**
```json
{
  "success": true,
  "tracking": {
    "email_sent_at": "2025-01-01T00:00:00Z",
    "email_opened_at": "2025-01-01T00:02:00Z",
    "link_clicked_at": "2025-01-01T00:03:00Z"
  }
}
```

---

## 🏥 Health & Monitoring

### Health Check
```http
GET /health
```
**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-01T00:00:00Z",
  "services": {
    "database": "connected",
    "redis": "connected",
    "jobProcessor": "running"
  }
}
```

### Metrics
```http
GET /metrics
```
**Response:**
```json
{
  "uptime": 3600,
  "memory": {
    "used": 51200000,
    "total": **********
  },
  "requests": {
    "total": 1000,
    "successful": 950,
    "failed": 50
  }
}
```

---

## 🚨 Error Responses

### Standard Error Format
```json
{
  "error": "Error message",
  "message": "Detailed error description",
  "code": "ERROR_CODE"
}
```

### Common Error Codes
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient plan level)
- `404`: Not found
- `429`: Rate limit exceeded
- `500`: Internal server error

### Plan Limit Errors
```json
{
  "error": "Usage limit exceeded",
  "message": "You have reached your monthly limit of 25 events",
  "currentPlan": "free",
  "requiredPlan": "pro",
  "currentUsage": 25,
  "limit": 25
}
```

---

## 📝 Rate Limiting

### Limits by Endpoint
- **Authentication**: 100 requests per 15 minutes
- **Webhooks**: 300 requests per 15 minutes
- **General API**: 1000 requests per 15 minutes

### Rate Limit Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

---

## 🔧 Development Notes

### Environment Variables
```bash
# Required for all features
JWT_SECRET=your-secret-key
DATABASE_URL=postgresql://...
REDIS_URL=redis://...

# Stripe integration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_CLIENT_ID=ca_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email service
SENDGRID_API_KEY=SG...

# Slack integration
SLACK_BOT_TOKEN=xoxb-...
SLACK_CLIENT_ID=...
SLACK_CLIENT_SECRET=...
```

### Testing with cURL
```bash
# Get auth token
curl -X POST http://localhost:4000/api/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{"refreshToken": "your-refresh-token"}'

# Use token for authenticated requests
curl -H "Authorization: Bearer <token>" \
  http://localhost:4000/api/billing/usage
```

---

## 📚 SDK Examples

### JavaScript/TypeScript
```typescript
class ChurnBotAPI {
  private baseUrl: string;
  private token: string;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  async getUsage() {
    const response = await fetch(`${this.baseUrl}/api/billing/usage`, {
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });
    return response.json();
  }

  async createSurvey(surveyData: any) {
    const response = await fetch(`${this.baseUrl}/api/surveys`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(surveyData)
    });
    return response.json();
  }
}
```

### Python
```python
import requests

class ChurnBotAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {token}'}
    
    def get_usage(self):
        response = requests.get(
            f'{self.base_url}/api/billing/usage',
            headers=self.headers
        )
        return response.json()
    
    def create_survey(self, survey_data):
        response = requests.post(
            f'{self.base_url}/api/surveys',
            headers={**self.headers, 'Content-Type': 'application/json'},
            json=survey_data
        )
        return response.json()
```

---

This API documentation provides all the endpoints needed to build a complete frontend for the Churn-Exit Survey Bot SaaS application. The endpoints support the full user journey from authentication through survey management and billing. 