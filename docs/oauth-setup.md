# OAuth Setup Guide

This guide explains how to set up OAuth authentication for both Stripe and Slack integrations in the Churn-Exit Survey Bot.

## Stripe OAuth Setup

### 1. Create Stripe Connect Application

1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Developers** → **Connect** → **Applications**
3. Click **Create application**
4. Fill in the application details:
   - **Name**: Churn-Exit Survey Bot
   - **Description**: Automated churn survey delivery
   - **Website**: Your app's domain
   - **Support email**: Your support email

### 2. Configure OAuth Settings

1. In your Stripe Connect app settings:
   - **Redirect URI**: `https://your-domain.com/auth/stripe/oauth/callback`
   - **OAuth scopes**: Select `read_write` for full access
   - **Webhook endpoints**: Add your webhook URL

2. Copy the following credentials:
   - **Client ID**: `ca_...` (starts with `ca_`)
   - **Secret Key**: `sk_test_...` or `sk_live_...`

### 3. Environment Variables

Add these to your `.env` file:

```bash
# Stripe OAuth
STRIPE_CLIENT_ID=ca_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

## Slack OAuth Setup

### 1. Create Slack App

1. Go to [Slack API](https://api.slack.com/apps)
2. Click **Create New App** → **From scratch**
3. Fill in the app details:
   - **App Name**: Churn-Exit Survey Bot
   - **Workspace**: Select your workspace

### 2. Configure OAuth Settings

1. In your Slack app settings:
   - Go to **OAuth & Permissions**
   - **Redirect URLs**: Add `https://your-domain.com/auth/slack/oauth/callback`
   - **Scopes**: Add the following bot token scopes:
     - `chat:write` - Send messages to channels
     - `channels:read` - View basic channel info
     - `users:read` - View basic user info

2. Copy the following credentials:
   - **Client ID**: Found in **Basic Information**
   - **Client Secret**: Found in **OAuth & Permissions**

### 3. Environment Variables

Add these to your `.env` file:

```bash
# Slack OAuth
SLACK_CLIENT_ID=...
SLACK_CLIENT_SECRET=...
SLACK_REDIRECT_URI=https://your-domain.com/auth/slack/oauth/callback
SLACK_BOT_TOKEN=xoxb-... (will be obtained after OAuth)
```

## OAuth Flow Implementation

### Frontend Integration

```javascript
// Stripe OAuth
async function connectStripe() {
  const response = await fetch('/auth/stripe/oauth');
  const { oauthUrl } = await response.json();
  window.location.href = oauthUrl;
}

// Slack OAuth
async function connectSlack() {
  const response = await fetch('/auth/slack/oauth');
  const { oauthUrl } = await response.json();
  window.location.href = oauthUrl;
}
```

### Backend Endpoints

The backend provides these OAuth endpoints:

#### Stripe OAuth
- `GET /auth/stripe/oauth` - Generate OAuth URL
- `GET /auth/stripe/oauth/callback` - Handle OAuth callback

#### Slack OAuth
- `GET /auth/slack/oauth` - Generate OAuth URL
- `GET /auth/slack/oauth/callback` - Handle OAuth callback

#### Customer Portal Setup
- `GET /auth/redirect-url` - Get redirect URL for Stripe Customer Portal

## OAuth Flow Steps

### Stripe OAuth Flow

1. **User initiates connection**:
   ```
   GET /auth/stripe/oauth
   Response: { oauthUrl: "https://connect.stripe.com/oauth/authorize?..." }
   ```

2. **User authorizes**:
   - User is redirected to Stripe OAuth page
   - User authorizes the application
   - Stripe redirects to callback URL with authorization code

3. **Backend processes callback**:
   ```
   GET /auth/stripe/oauth/callback?code=...&state=...
   Response: { success: true, user: {...}, token: "jwt_token" }
   ```

4. **User account created/updated**:
   - Stripe account details stored
   - JWT token generated for session
   - User redirected to dashboard

### Slack OAuth Flow

1. **User initiates connection**:
   ```
   GET /auth/slack/oauth
   Response: { oauthUrl: "https://slack.com/oauth/v2/authorize?..." }
   ```

2. **User authorizes**:
   - User is redirected to Slack OAuth page
   - User selects workspace and authorizes
   - Slack redirects to callback URL with authorization code

3. **Backend processes callback**:
   ```
   GET /auth/slack/oauth/callback?code=...&state=...
   Response: { success: true, workspace: {...} }
   ```

4. **Workspace connected**:
   - Slack workspace details stored
   - Bot token obtained and stored
   - User can now receive notifications

## Security Considerations

### State Parameter
- Always use a unique `state` parameter to prevent CSRF attacks
- Validate the state parameter in callbacks

### Token Storage
- Access tokens are encrypted before storage
- Refresh tokens are stored securely
- Tokens are rotated regularly

### Error Handling
- OAuth errors are logged but not exposed to users
- Graceful fallback for failed connections
- Clear error messages for common issues

## Testing OAuth

### Development Environment

1. **Local testing**:
   ```bash
   # Start the development server
   npm run dev
   
   # Test OAuth endpoints
   curl http://localhost:3000/auth/stripe/oauth
   curl http://localhost:3000/auth/slack/oauth
   ```

2. **Ngrok for callbacks**:
   ```bash
   # Install ngrok
   npm install -g ngrok
   
   # Expose local server
   ngrok http 3000
   
   # Use ngrok URL in OAuth redirect URIs
   ```

### Production Deployment

1. **Update redirect URIs**:
   - Stripe: `https://your-domain.com/auth/stripe/oauth/callback`
   - Slack: `https://your-domain.com/auth/slack/oauth/callback`

2. **Environment variables**:
   - Set all required OAuth environment variables
   - Use production Stripe keys (`sk_live_...`)
   - Use production Slack app credentials

3. **SSL/TLS**:
   - OAuth requires HTTPS in production
   - Ensure SSL certificates are valid
   - Configure proper security headers

## Troubleshooting

### Common Issues

1. **Invalid redirect URI**:
   - Check that redirect URIs match exactly
   - Include protocol (https://)
   - No trailing slashes

2. **Missing scopes**:
   - Ensure all required scopes are requested
   - Check Slack app permissions
   - Verify Stripe Connect settings

3. **Token exchange failures**:
   - Check client ID and secret
   - Verify code hasn't expired
   - Ensure proper request format

### Debug Logging

Enable debug logging to troubleshoot OAuth issues:

```bash
LOG_LEVEL=debug npm run dev
```

Check logs for:
- OAuth URL generation
- Token exchange attempts
- Account detail retrieval
- Error responses from providers

## Next Steps

After OAuth setup:

1. **Test webhook delivery** to Stripe
2. **Test Slack notifications** for churn events
3. **Configure Customer Portal** redirect URL
4. **Set up monitoring** for OAuth flows
5. **Implement token refresh** logic

The OAuth implementation is now production-ready and handles both Stripe and Slack authentication securely! 🚀 