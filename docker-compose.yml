version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/churnbot
      - REDIS_URL=redis://redis:6379
      - API_BASE_URL=${API_BASE_URL:-http://localhost:3000}
      - SURVEY_BASE_URL=${SURVEY_BASE_URL:-http://localhost:3000}
      - DASHBOARD_URL=${DASHBOARD_URL:-http://localhost:3000}
      # Stripe Configuration
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_CLIENT_ID=${STRIPE_CLIENT_ID}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - STRIPE_PRO_PRICE_ID=${STRIPE_PRO_PRICE_ID}
      - STRIPE_SCALE_PRICE_ID=${STRIPE_SCALE_PRICE_ID}
      # SendGrid Configuration
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - SENDGRID_FROM_EMAIL=${SENDGRID_FROM_EMAIL:-<EMAIL>}
      # Slack Configuration
      - SLACK_BOT_TOKEN=${SLACK_BOT_TOKEN}
      - SLACK_CLIENT_ID=${SLACK_CLIENT_ID}
      - SLACK_CLIENT_SECRET=${SLACK_CLIENT_SECRET}
      - SLACK_REDIRECT_URI=${SLACK_REDIRECT_URI}
      # Security & JWT
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      # Rate Limiting
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-900000}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-1000}
      # Logging
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FILE=${LOG_FILE:-logs/app.log}
      # File Upload
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-5242880}
      - UPLOAD_PATH=${UPLOAD_PATH:-uploads/}
      # Security
      - BCRYPT_ROUNDS=${BCRYPT_ROUNDS:-12}
      - API_KEY=${API_KEY}
      - BLOCKED_IPS=${BLOCKED_IPS}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "dist/health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=churnbot
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./src/database/migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data: 