{"name": "churn-exit-survey-bot", "version": "1.0.0", "description": "Single-purpose SaaS for capturing churn insights via Stripe webhooks and Customer Portal redirects", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx watch src/index.ts", "test": "vitest", "test:ui": "vitest --ui", "test:cov": "vitest run --coverage", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "drizzle:generate": "drizzle-kit generate", "drizzle:migrate": "drizzle-kit migrate", "drizzle:studio": "drizzle-kit studio", "migrate": "tsx scripts/migrate.ts"}, "keywords": ["churn", "stripe", "survey", "saas", "webhooks"], "author": "Churn-Exit Survey Bot", "license": "MIT", "dependencies": {"@sendgrid/mail": "^8.1.1", "@slack/web-api": "^6.10.0", "@types/node-cron": "^3.0.11", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.6.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.7.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0-rc.3", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pg": "^8.16.3", "redis": "^4.6.10", "sharp": "^0.32.6", "stripe": "^14.10.0", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^4.0.15"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "jest": "^29.7.0", "prettier": "^3.6.2", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "tsx": "^4.20.3", "typescript": "^5.3.2", "typescript-eslint": "^8.39.0", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}