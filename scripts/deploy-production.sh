#!/bin/bash

# Production deployment script for Churn-Exit Survey Bot
set -e

echo "🚀 Starting production deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if required environment variables are set
echo "🔍 Checking environment variables..."

required_vars=(
    "DATABASE_URL"
    "STRIPE_SECRET_KEY"
    "STRIPE_CLIENT_ID"
    "STRIPE_WEBHOOK_SECRET"
    "SENDGRID_API_KEY"
    "SLACK_CLIENT_ID"
    "SLACK_CLIENT_SECRET"
    "SLACK_REDIRECT_URI"
    "JWT_SECRET"
    "API_BASE_URL"
    "SURVEY_BASE_URL"
    "DASHBOARD_URL"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    print_error "Missing required environment variables:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    exit 1
fi

print_status "Environment variables validated"

# Check if Docker and Docker Compose are installed
echo "🔍 Checking Docker installation..."
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed"
    exit 1
fi

print_status "Docker and Docker Compose are available"

# Check if SSL certificates exist (for production)
if [ "$NODE_ENV" = "production" ]; then
    echo "🔍 Checking SSL certificates..."
    if [ ! -f "./ssl/cert.pem" ] || [ ! -f "./ssl/key.pem" ]; then
        print_warning "SSL certificates not found. Creating self-signed certificates for development..."
        mkdir -p ssl
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout ssl/key.pem -out ssl/cert.pem \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
    fi
    print_status "SSL certificates ready"
fi

# Build the application
echo "📦 Building application..."
docker-compose build --no-cache
print_status "Application built successfully"

# Run database migrations
echo "🗄️ Running database migrations..."
docker-compose run --rm app npm run migrate
print_status "Database migrations completed"

# Start services
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
timeout=120
counter=0

while [ $counter -lt $timeout ]; do
    if docker-compose ps | grep -q "healthy"; then
        print_status "All services are healthy"
        break
    fi
    
    echo "Waiting for services to be healthy... ($counter/$timeout seconds)"
    sleep 5
    counter=$((counter + 5))
done

if [ $counter -eq $timeout ]; then
    print_error "Services failed to become healthy within $timeout seconds"
    docker-compose logs
    exit 1
fi

# Health check
echo "🏥 Running health check..."
sleep 10  # Give the app time to fully start

health_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/health || echo "000")

if [ "$health_response" = "200" ]; then
    print_status "Health check passed"
else
    print_error "Health check failed (HTTP $health_response)"
    docker-compose logs app
    exit 1
fi

# Performance check
echo "⚡ Running performance check..."
metrics_response=$(curl -s http://localhost:3000/metrics | jq -r '.health.database' 2>/dev/null || echo "false")

if [ "$metrics_response" = "true" ]; then
    print_status "Performance check passed"
else
    print_warning "Performance check failed, but continuing deployment"
fi

# Test OAuth endpoints
echo "🔐 Testing OAuth endpoints..."
stripe_oauth=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/auth/stripe/oauth || echo "000")
slack_oauth=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/auth/slack/oauth || echo "000")

if [ "$stripe_oauth" = "200" ] && [ "$slack_oauth" = "200" ]; then
    print_status "OAuth endpoints are working"
else
    print_warning "OAuth endpoints returned HTTP $stripe_oauth and $slack_oauth"
fi

# Log deployment
echo "📝 Logging deployment..."
deploy_data=$(cat <<EOF
{
  "version": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
  "environment": "production",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "services": ["app", "postgres", "redis", "nginx"]
}
EOF
)

curl -X POST http://localhost:3000/api/deployments \
  -H "Content-Type: application/json" \
  -d "$deploy_data" || print_warning "Failed to log deployment (non-critical)"

# Final status
echo ""
print_status "🎉 Deployment completed successfully!"
echo ""
echo "📊 Application URLs:"
echo "  - API: http://localhost:3000"
echo "  - Health: http://localhost:3000/health"
echo "  - Metrics: http://localhost:3000/metrics (protected)"
echo ""
echo "🔐 OAuth Endpoints:"
echo "  - Stripe OAuth: http://localhost:3000/auth/stripe/oauth"
echo "  - Slack OAuth: http://localhost:3000/auth/slack/oauth"
echo ""
echo "📋 Next Steps:"
echo "  1. Configure your domain in environment variables"
echo "  2. Set up SSL certificates for production"
echo "  3. Configure Stripe and Slack OAuth apps"
echo "  4. Test webhook delivery"
echo "  5. Monitor application logs"
echo ""
echo "📝 Useful Commands:"
echo "  - View logs: docker-compose logs -f"
echo "  - Restart app: docker-compose restart app"
echo "  - Stop services: docker-compose down"
echo "  - Update app: docker-compose pull && docker-compose up -d" 