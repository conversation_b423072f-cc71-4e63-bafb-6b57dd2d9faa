#!/bin/bash

# Churn-Exit Survey Bot - Production Deployment Script
# This script sets up the application for production deployment

set -e

echo "🚀 Starting Churn-Exit Survey Bot deployment..."

# Check if required environment variables are set
required_vars=(
  "DATABASE_URL"
  "REDIS_URL"
  "JWT_SECRET"
  "STRIPE_SECRET_KEY"
  "STRIPE_CLIENT_ID"
  "STRIPE_WEBHOOK_SECRET"
  "SENDGRID_API_KEY"
  "SLACK_BOT_TOKEN"
  "SLACK_CLIENT_ID"
  "SLACK_CLIENT_SECRET"
)

echo "📋 Checking environment variables..."
for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    echo "❌ Error: $var is not set"
    exit 1
  fi
done

echo "✅ All required environment variables are set"

# Install dependencies
echo "📦 Installing dependencies..."
npm ci --only=production

# Build the application
echo "🔨 Building application..."
npm run build

# Run database migrations
echo "🗄️ Running database migrations..."
npm run migrate

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p uploads

# Set proper permissions
echo "🔐 Setting permissions..."
chmod 755 dist/
chmod 644 logs/
chmod 644 uploads/

# Health check
echo "🏥 Running health check..."
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
  echo "✅ Health check passed"
else
  echo "⚠️ Health check failed - server may not be running"
fi

echo "🎉 Deployment completed successfully!"
echo ""
echo "📊 Next steps:"
echo "1. Set up SSL/TLS certificates"
echo "2. Configure reverse proxy (Nginx)"
echo "3. Set up monitoring and logging"
echo "4. Configure Stripe webhook endpoints"
echo "5. Set up Slack app configuration"
echo ""
echo "🔗 Useful endpoints:"
echo "- Health: GET /health"
echo "- Metrics: GET /metrics"
echo "- API Docs: GET /api/docs" 