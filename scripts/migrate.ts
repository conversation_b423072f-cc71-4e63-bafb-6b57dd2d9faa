import { drizzle } from 'drizzle-orm/node-postgres';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import { Pool } from 'pg';
import config from '../src/config';
import logger from '../src/utils/logger';

async function runMigrations() {
  const pool = new Pool({
    connectionString: config.databaseUrl,
  });

  const db = drizzle(pool);

  try {
    logger.info('Starting database migrations...');
    
    // Create tables if they don't exist
    await db.execute(`
      -- Users table
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) NOT NULL UNIQUE,
        name VARCHAR(255),
        plan_type VARCHAR(20) NOT NULL DEFAULT 'free',
        stripe_customer_id VARCHAR(255),
        stripe_access_token TEXT,
        stripe_refresh_token TEXT,
        slack_workspace_id VARCHAR(255),
        slack_workspace_name <PERSON><PERSON><PERSON><PERSON>(255),
        slack_access_token TEXT,
        slack_bot_token TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW()
      );

      -- Surveys table
      CREATE TABLE IF NOT EXISTS surveys (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        question_text TEXT NOT NULL,
        options JSONB NOT NULL,
        branding JSONB,
        slack_channel_id VARCHAR(255) NOT NULL,
        slack_workspace_id VARCHAR(255) NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT true,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );

      -- Events table
      CREATE TABLE IF NOT EXISTS events (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        stripe_event_id VARCHAR(255) NOT NULL UNIQUE,
        event_type VARCHAR(100) NOT NULL,
        customer_id VARCHAR(255),
        subscription_id VARCHAR(255),
        plan_name VARCHAR(100),
        mrr_lost INTEGER,
        canceled_at TIMESTAMP,
        customer_email VARCHAR(255),
        customer_name VARCHAR(255),
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );

      -- Responses table
      CREATE TABLE IF NOT EXISTS responses (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
        survey_id UUID NOT NULL REFERENCES surveys(id) ON DELETE CASCADE,
        token VARCHAR(255) NOT NULL UNIQUE,
        selected_option VARCHAR(255),
        custom_text TEXT,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        responded_at TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );

      -- Usage logs table
      CREATE TABLE IF NOT EXISTS usage_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        month VARCHAR(7) NOT NULL,
        events_count INTEGER NOT NULL DEFAULT 0,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        UNIQUE(user_id, month)
      );

      -- Email tracking table
      CREATE TABLE IF NOT EXISTS email_tracking (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        response_id UUID NOT NULL REFERENCES responses(id) ON DELETE CASCADE,
        email_sent_at TIMESTAMP NOT NULL,
        email_opened_at TIMESTAMP,
        link_clicked_at TIMESTAMP,
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      );
    `);

    // Create indexes
    await db.execute(`
      -- Survey indexes
      CREATE INDEX IF NOT EXISTS surveys_user_idx ON surveys(user_id);
      CREATE INDEX IF NOT EXISTS surveys_active_idx ON surveys(is_active);

      -- Event indexes
      CREATE INDEX IF NOT EXISTS events_user_idx ON events(user_id);
      CREATE INDEX IF NOT EXISTS events_stripe_event_idx ON events(stripe_event_id);
      CREATE INDEX IF NOT EXISTS events_customer_idx ON events(customer_id);

      -- Response indexes
      CREATE INDEX IF NOT EXISTS responses_event_idx ON responses(event_id);
      CREATE INDEX IF NOT EXISTS responses_survey_idx ON responses(survey_id);
      CREATE INDEX IF NOT EXISTS responses_token_idx ON responses(token);
      CREATE INDEX IF NOT EXISTS responses_status_idx ON responses(status);

      -- Usage log indexes
      CREATE INDEX IF NOT EXISTS usage_logs_user_idx ON usage_logs(user_id);

      -- Email tracking indexes
      CREATE INDEX IF NOT EXISTS email_tracking_response_idx ON email_tracking(response_id);
    `);

    logger.info('Database migrations completed successfully');
  } catch (error) {
    logger.error('Migration failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations()
    .then(() => {
      logger.info('Migrations completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Migration failed:', error);
      process.exit(1);
    });
}

export default runMigrations; 