{"id": "96f6a6c4-483e-489d-8c7e-5fe83715acfa", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.events": {"name": "events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "stripe_event_id": {"name": "stripe_event_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "plan_name": {"name": "plan_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "mrr_lost": {"name": "mrr_lost", "type": "numeric(12, 2)", "primaryKey": false, "notNull": true}, "canceled_at": {"name": "canceled_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "processed_at": {"name": "processed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "customer_email": {"name": "customer_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "customer_name": {"name": "customer_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {"events_stripe_event_unique": {"name": "events_stripe_event_unique", "columns": [{"expression": "stripe_event_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "events_user_idx": {"name": "events_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "events_processed_idx": {"name": "events_processed_idx", "columns": [{"expression": "processed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"events_user_id_users_id_fk": {"name": "events_user_id_users_id_fk", "tableFrom": "events", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.responses": {"name": "responses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_id": {"name": "event_id", "type": "uuid", "primaryKey": false, "notNull": true}, "survey_id": {"name": "survey_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "selected_option": {"name": "selected_option", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "custom_text": {"name": "custom_text", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "responded_at": {"name": "responded_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "slack_sent_at": {"name": "slack_sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "follow_up_sent_at": {"name": "follow_up_sent_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "no_response_at": {"name": "no_response_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "email_opened_at": {"name": "email_opened_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "open_count": {"name": "open_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "hover_data": {"name": "hover_data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"responses_token_unique": {"name": "responses_token_unique", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "responses_survey_idx": {"name": "responses_survey_idx", "columns": [{"expression": "survey_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"responses_event_id_events_id_fk": {"name": "responses_event_id_events_id_fk", "tableFrom": "responses", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "responses_survey_id_surveys_id_fk": {"name": "responses_survey_id_surveys_id_fk", "tableFrom": "responses", "tableTo": "surveys", "columnsFrom": ["survey_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.surveys": {"name": "surveys", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "question_text": {"name": "question_text", "type": "text", "primaryKey": false, "notNull": true}, "options": {"name": "options", "type": "jsonb", "primaryKey": false, "notNull": true}, "branding": {"name": "branding", "type": "jsonb", "primaryKey": false, "notNull": true}, "slack_channel_id": {"name": "slack_channel_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slack_workspace_id": {"name": "slack_workspace_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "follow_up_enabled": {"name": "follow_up_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "tracking_enabled": {"name": "tracking_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"surveys_user_idx": {"name": "surveys_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "surveys_active_idx": {"name": "surveys_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"surveys_user_id_users_id_fk": {"name": "surveys_user_id_users_id_fk", "tableFrom": "surveys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.usage_logs": {"name": "usage_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "month_year": {"name": "month_year", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": true}, "events_count": {"name": "events_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"usage_logs_user_month_unique": {"name": "usage_logs_user_month_unique", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "month_year", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"usage_logs_user_id_users_id_fk": {"name": "usage_logs_user_id_users_id_fk", "tableFrom": "usage_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "uid": {"name": "uid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_account_id": {"name": "stripe_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_access_token": {"name": "stripe_access_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_refresh_token": {"name": "stripe_refresh_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "plan_type": {"name": "plan_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'free'"}, "slack_workspace_id": {"name": "slack_workspace_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "slack_workspace_name": {"name": "slack_workspace_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "slack_access_token": {"name": "slack_access_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "slack_bot_token": {"name": "slack_bot_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "users_uid_unique": {"name": "users_uid_unique", "columns": [{"expression": "uid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "users_stripe_cust_idx": {"name": "users_stripe_cust_idx", "columns": [{"expression": "stripe_customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_events": {"name": "webhook_events", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "event_id": {"name": "event_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "text", "primaryKey": false, "notNull": true}, "received_at": {"name": "received_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "processed": {"name": "processed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "processed_at": {"name": "processed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"webhook_events_event_unique": {"name": "webhook_events_event_unique", "columns": [{"expression": "event_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}