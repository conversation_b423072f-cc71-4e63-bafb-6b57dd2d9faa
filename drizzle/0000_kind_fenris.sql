CREATE TABLE "events" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"stripe_event_id" varchar(255) NOT NULL,
	"event_type" varchar(255) NOT NULL,
	"customer_id" varchar(255) NOT NULL,
	"subscription_id" varchar(255) NOT NULL,
	"plan_name" varchar(255) NOT NULL,
	"mrr_lost" numeric(12, 2) NOT NULL,
	"canceled_at" timestamp with time zone NOT NULL,
	"processed_at" timestamp with time zone NOT NULL,
	"customer_email" varchar(255),
	"customer_name" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "responses" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"event_id" uuid NOT NULL,
	"survey_id" uuid NOT NULL,
	"token" varchar(255) NOT NULL,
	"selected_option" varchar(255),
	"custom_text" text,
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"responded_at" timestamp with time zone,
	"slack_sent_at" timestamp with time zone,
	"follow_up_sent_at" timestamp with time zone,
	"no_response_at" timestamp with time zone,
	"email_opened_at" timestamp with time zone,
	"open_count" integer DEFAULT 0 NOT NULL,
	"hover_data" jsonb
);
--> statement-breakpoint
CREATE TABLE "surveys" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"question_text" text NOT NULL,
	"options" jsonb NOT NULL,
	"branding" jsonb NOT NULL,
	"slack_channel_id" varchar(255) NOT NULL,
	"slack_workspace_id" varchar(255) NOT NULL,
	"follow_up_enabled" boolean DEFAULT false NOT NULL,
	"tracking_enabled" boolean DEFAULT false NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "usage_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"month_year" varchar(7) NOT NULL,
	"events_count" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"uid" varchar(255) NOT NULL,
	"stripe_customer_id" varchar(255),
	"stripe_account_id" varchar(255),
	"stripe_access_token" varchar(255),
	"stripe_refresh_token" varchar(255),
	"email" varchar(255) NOT NULL,
	"plan_type" varchar(20) DEFAULT 'free' NOT NULL,
	"slack_workspace_id" varchar(255),
	"slack_workspace_name" varchar(255),
	"slack_access_token" varchar(255),
	"slack_bot_token" varchar(255),
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "webhook_events" (
	"id" serial PRIMARY KEY NOT NULL,
	"provider" varchar(100) NOT NULL,
	"event_id" varchar(255) NOT NULL,
	"payload" text NOT NULL,
	"received_at" timestamp with time zone DEFAULT now() NOT NULL,
	"processed" boolean DEFAULT false NOT NULL,
	"processed_at" timestamp with time zone
);
--> statement-breakpoint
ALTER TABLE "events" ADD CONSTRAINT "events_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "responses" ADD CONSTRAINT "responses_event_id_events_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "responses" ADD CONSTRAINT "responses_survey_id_surveys_id_fk" FOREIGN KEY ("survey_id") REFERENCES "public"."surveys"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "surveys" ADD CONSTRAINT "surveys_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "usage_logs" ADD CONSTRAINT "usage_logs_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "events_stripe_event_unique" ON "events" USING btree ("stripe_event_id");--> statement-breakpoint
CREATE INDEX "events_user_idx" ON "events" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "events_processed_idx" ON "events" USING btree ("processed_at");--> statement-breakpoint
CREATE UNIQUE INDEX "responses_token_unique" ON "responses" USING btree ("token");--> statement-breakpoint
CREATE INDEX "responses_survey_idx" ON "responses" USING btree ("survey_id");--> statement-breakpoint
CREATE INDEX "surveys_user_idx" ON "surveys" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "surveys_active_idx" ON "surveys" USING btree ("is_active");--> statement-breakpoint
CREATE UNIQUE INDEX "usage_logs_user_month_unique" ON "usage_logs" USING btree ("user_id","month_year");--> statement-breakpoint
CREATE UNIQUE INDEX "users_email_unique" ON "users" USING btree ("email");--> statement-breakpoint
CREATE UNIQUE INDEX "users_uid_unique" ON "users" USING btree ("uid");--> statement-breakpoint
CREATE INDEX "users_stripe_cust_idx" ON "users" USING btree ("stripe_customer_id");--> statement-breakpoint
CREATE UNIQUE INDEX "webhook_events_event_unique" ON "webhook_events" USING btree ("event_id");