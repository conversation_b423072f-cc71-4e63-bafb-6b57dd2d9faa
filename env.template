# Database
DATABASE_URL=postgresql://username:password@localhost:5432/churnbot

# Redis
REDIS_URL=redis://localhost:6379

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-here
JWT_ACCESS_EXPIRES=15m
JWT_REFRESH_EXPIRES=7d
JWT_ALG=HS256

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_CLIENT_ID=ca_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PRO_PRICE_ID=price_...
STRIPE_SCALE_PRICE_ID=price_...

# SendGrid Email
SENDGRID_API_KEY=SG...
SENDGRID_FROM_EMAIL=<EMAIL>

# Slack Integration
SLACK_BOT_TOKEN=xoxb-...
SLACK_CLIENT_ID=...
SLACK_CLIENT_SECRET=...
SLACK_REDIRECT_URI=https://yourdomain.com/api/auth/slack/oauth/callback

# Application URLs
API_BASE_URL=https://api.yourdomain.com
SURVEY_BASE_URL=https://yourdomain.com
DASHBOARD_URL=https://app.yourdomain.com

# Security & Rate Limiting
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
RATE_LIMIT_AUTH=100:15m
RATE_LIMIT_WEBHOOKS=300:15m
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Development
NODE_ENV=development
PORT=3000 