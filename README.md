# Churn-Exit Survey Bot

A production-ready TypeScript backend for automatically surveying customers when they cancel Stripe subscriptions. Built with Express.js, PostgreSQL, and real-time Slack notifications.

## Features

- **Real-time Webhook Processing**: Instant detection of subscription cancellations via Stripe webhooks
- **Customer Portal Integration**: Direct survey access via Stripe Customer Portal redirects
- **Email Surveys**: One-click survey emails with tracking and follow-up capabilities
- **Slack Notifications**: Real-time churn insights with emoji-coded responses
- **Usage Tracking**: Plan-based limits with contextual upgrade prompts
- **Non-response Data**: Treats non-response as valuable data with passive signal capture
- **Production Ready**: Comprehensive error handling, logging, and monitoring

## Tech Stack

- **Runtime**: Node.js 18+ with TypeScript
- **Framework**: Express.js with middleware for security and performance
- **Database**: PostgreSQL with connection pooling
- **Cache**: Redis for session management and job queues
- **Email**: SendGrid for transactional emails
- **Payments**: Stripe for webhooks, OAuth, and checkout
- **Notifications**: Slack API for real-time alerts
- **Deployment**: Fly.io or Railway ready

## Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- Stripe account
- SendGrid account
- Slack workspace

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd churn-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   # Create database
   createdb churnbot

   # Run migrations
   npm run migrate
   ```

5. **Build and start**
   ```bash
   npm run build
   npm start
   ```

### Environment Variables

Required environment variables (see `env.example`):

```bash
# Server Configuration
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000
SURVEY_BASE_URL=http://localhost:3000
DASHBOARD_URL=http://localhost:3000

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/churnbot
REDIS_URL=redis://localhost:6379

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PUBLISHABLE_KEY=pk_test_...

# SendGrid Configuration
SENDGRID_API_KEY=SG...
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Your Company Name

# Slack Configuration
SLACK_BOT_TOKEN=xoxb-...
SLACK_SIGNING_SECRET=...

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d
```

## API Endpoints

For a complete, detailed reference (paths, methods, params, example payloads), see:
- [docs/API.md](docs/API.md:1)

### Webhooks
- `POST /webhooks/:uid` - Stripe webhook processing
- `POST /webhooks/survey/respond/:token` - Survey response collection
- `GET /webhooks/tracking/pixel/:token` - Email open tracking
- `POST /webhooks/tracking/hover/:token/:option` - Click/hover tracking

### Authentication
- `GET /api/auth/stripe/oauth` - Stripe OAuth initiation
- `GET /api/auth/stripe/callback` - OAuth callback handling
- `GET /api/auth/redirect-url` - Get Customer Portal redirect URL

### Users
- `GET /api/users/me` - Get current user
- `GET /api/users/usage` - Get usage statistics
- `GET /api/users/events` - Get user events
- `GET /api/users/events/export` - Export events (Pro plan)

### Surveys
- `POST /api/surveys` - Create survey
- `GET /api/surveys/active` - Get active survey
- `PUT /api/surveys/:id` - Update survey
- `GET /api/surveys/:id` - Get survey by ID

### Tracking
- `GET /api/tracking/pixel/:token` - Email tracking pixel
- `POST /api/tracking/hover/:token/:option` - Hover tracking

## Database Schema

### Core Tables

- **users**: User accounts with Stripe integration
- **surveys**: Survey configurations and branding
- **events**: Stripe webhook events and churn data
- **responses**: Survey responses with tracking data
- **usage_logs**: Monthly usage tracking per user

### Key Features

- **UUID primary keys** for security
- **JSONB fields** for flexible data storage
- **Comprehensive indexing** for performance
- **Foreign key constraints** for data integrity
- **Automatic timestamps** for auditing

## Development

### Scripts

```bash
# Development
npm run dev           # Start with hot reload
npm run build         # Build for production
npm start             # Start production server

# Testing
npm test              # Run tests
npm run test:ui       # Vitest UI
npm run test:cov      # Coverage report

# Code Quality
npm run lint          # ESLint check
npm run format        # Prettier write
npm run typecheck     # TypeScript type checking

# Database (Drizzle)
npm run drizzle:generate  # Generate SQL from schema
npm run drizzle:migrate   # Apply migrations
npm run drizzle:studio    # Open Drizzle Studio
```

### Project Structure

```
src/
├── config/          # Configuration management
├── database/        # Database migrations and seeds
├── middleware/      # Express middleware
├── routes/          # API route handlers
├── services/        # Business logic services
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
└── index.ts         # Application entry point
```

## Deployment

### Fly.io

1. **Install Fly CLI**
   ```bash
   curl -L https://fly.io/install.sh | sh
   ```

2. **Deploy**
   ```bash
   fly launch
   fly deploy
   ```

### Railway

1. **Connect repository**
   ```bash
   railway login
   railway link
   ```

2. **Deploy**
   ```bash
   railway up
   ```

## Monitoring & Observability

### Logging

- **Winston logger** with structured JSON output
- **Request/response logging** with performance metrics
- **Error tracking** with stack traces
- **File rotation** with size limits

### Health Checks

- `GET /health` - Application health status
- Database connectivity monitoring
- External service status checks

### Metrics

- Webhook processing latency
- Email delivery success rate
- Survey response rate
- Database query performance
- Usage tracking accuracy

## Security

### Webhook Security

- **Stripe signature verification** on all webhooks
- **Rate limiting** to prevent abuse
- **Event deduplication** using Stripe event IDs

### Data Protection

- **PII encryption** for customer data
- **GDPR compliance** for EU customers
- **Data retention policies**
- **Secure token generation**

### API Security

- **Helmet.js** for security headers
- **CORS configuration** for cross-origin requests
- **Rate limiting** on all endpoints
- **Input validation** with express-validator

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the technical report in `docs/technical-report.md` 