import cron from 'node-cron';
import redisService from './redis';
import emailService from './email';
import slackService from './slack-simple';
import surveyService from './survey';
import database from './database';
import logger from '../utils/logger';

class JobProcessor {
  private isRunning = false;

  constructor() {
    this.setupCronJobs();
  }

  private setupCronJobs(): void {
    // Process follow-up emails every hour
    cron.schedule('0 * * * *', async () => {
      await this.processFollowUpEmails();
    });

    // Mark no-responses every hour
    cron.schedule('30 * * * *', async () => {
      await this.processNoResponses();
    });

    // Process job queue every 30 seconds
    cron.schedule('*/30 * * * * *', async () => {
      await this.processJobQueue();
    });

    // Health check every 5 minutes
    cron.schedule('*/5 * * * *', async () => {
      await this.healthCheck();
    });

    logger.info('Background job processor started');
  }

  async processFollowUpEmails(): Promise<void> {
    try {
      const pendingResponses = await surveyService.getPendingResponses(72); // 72 hours old
      
      for (const response of pendingResponses) {
        try {
          const survey = await database.getSurveyById(response.survey_id);
          if (!survey) continue;

          if (survey.follow_up_enabled) {
            await emailService.sendFollowUpEmail(response);
            await surveyService.updateResponse(response.token, {
              follow_up_sent_at: new Date()
            });
            logger.info('Follow-up email sent', { responseId: response.id });
          } else {
            await surveyService.markAsNoResponse(response.id);
            logger.info('Response marked as no-response', { responseId: response.id });
          }
        } catch (error) {
          logger.error('Error processing follow-up email', { 
            responseId: response.id, 
            error 
          });
        }
      }
    } catch (error) {
      logger.error('Follow-up email processing error:', error);
    }
  }

  async processNoResponses(): Promise<void> {
    try {
      const pendingResponses = await surveyService.getPendingResponses(96); // 96 hours old
      
      for (const response of pendingResponses) {
        try {
          await surveyService.markAsNoResponse(response.id);
          await slackService.sendNoResponseNotification(response);
          logger.info('No-response processed', { responseId: response.id });
        } catch (error) {
          logger.error('Error processing no-response', { 
            responseId: response.id, 
            error 
          });
        }
      }
    } catch (error) {
      logger.error('No-response processing error:', error);
    }
  }

  async processJobQueue(): Promise<void> {
    try {
      const job = await redisService.dequeueJob('email-queue');
      if (!job) return;

      switch (job.type) {
        case 'send-survey-email':
          await this.processSurveyEmailJob(job);
          break;
        case 'send-follow-up-email':
          await this.processFollowUpEmailJob(job);
          break;
        case 'send-slack-notification':
          await this.processSlackNotificationJob(job);
          break;
        default:
          logger.warn('Unknown job type:', job.type);
      }
    } catch (error) {
      logger.error('Job queue processing error:', error);
    }
  }

  private async processSurveyEmailJob(job: any): Promise<void> {
    try {
      const { eventId, surveyId, token } = job.data;
      const event = await database.getEventById(eventId);
      const survey = await database.getSurveyById(surveyId);
      
      if (event && survey) {
        await emailService.sendSurveyEmail({
          surveyId: survey.id,
          customerEmail: event.customer_email || '<EMAIL>',
          customerName: event.customer_name,
          stripeCustomerId: event.customer_id,
          surveyTitle: survey.question_text
        });
        logger.info('Survey email job processed', { eventId, surveyId });
      }
    } catch (error) {
      logger.error('Survey email job error:', error);
    }
  }

  private async processFollowUpEmailJob(job: any): Promise<void> {
    try {
      const { responseId } = job.data;
      const response = await surveyService.getResponseByToken(responseId);
      const survey = await database.getSurveyById(response?.survey_id || '');
      
      if (response && survey) {
        await emailService.sendFollowUpEmail(response);
        logger.info('Follow-up email job processed', { responseId });
      }
    } catch (error) {
      logger.error('Follow-up email job error:', error);
    }
  }

  private async processSlackNotificationJob(job: any): Promise<void> {
    try {
      const { responseId } = job.data;
      const response = await surveyService.getResponseByToken(responseId);
      
      if (response) {
        await slackService.sendNotification(response);
        logger.info('Slack notification job processed', { responseId });
      }
    } catch (error) {
      logger.error('Slack notification job error:', error);
    }
  }

  async enqueueEmailJob(type: string, data: any): Promise<void> {
    try {
      await redisService.enqueueJob('email-queue', {
        type,
        data,
        timestamp: new Date().toISOString()
      });
      logger.info('Email job enqueued', { type, data });
    } catch (error) {
      logger.error('Failed to enqueue email job:', error);
    }
  }

  async healthCheck(): Promise<void> {
    try {
      const redisHealth = await redisService.healthCheck();
      const dbHealth = await this.checkDatabaseHealth();
      
      if (!redisHealth || !dbHealth) {
        logger.error('Health check failed', { redisHealth, dbHealth });
      } else {
        logger.debug('Health check passed');
      }
    } catch (error) {
      logger.error('Health check error:', error);
    }
  }

  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      await database.query('SELECT 1');
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  async start(): Promise<void> {
    if (this.isRunning) return;
    
    this.isRunning = true;
    logger.info('Job processor started');
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    logger.info('Job processor stopped');
  }
}

export default new JobProcessor(); 