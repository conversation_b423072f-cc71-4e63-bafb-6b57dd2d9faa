import logger from '../utils/logger';
import database from './database';
import redisService from './redis';
import config from '../config';

interface MetricData {
  timestamp: Date;
  value: number;
  tags?: Record<string, string>;
}

interface HealthStatus {
  database: boolean;
  redis: boolean;
  externalServices: {
    stripe: boolean;
    sendgrid: boolean;
    slack: boolean;
  };
  uptime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: number;
}

class MonitoringService {
  private metrics: Map<string, MetricData[]> = new Map();
  private startTime: Date = new Date();

  constructor() {
    this.setupProcessMonitoring();
  }

  private setupProcessMonitoring(): void {
    // Monitor uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.recordMetric('uncaught_exception', 1, { error: error.message });
      logger.error('Uncaught Exception:', error);
    });

    // Monitor unhandled rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.recordMetric('unhandled_rejection', 1, { 
        reason: reason instanceof Error ? reason.message : String(reason) 
      });
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });

    // Monitor memory usage
    setInterval(() => {
      const memUsage = process.memoryUsage();
      this.recordMetric('memory_usage_mb', memUsage.heapUsed / 1024 / 1024);
      this.recordMetric('memory_rss_mb', memUsage.rss / 1024 / 1024);
    }, 60000); // Every minute
  }

  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    const metric: MetricData = {
      timestamp: new Date(),
      value,
      tags
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metricArray = this.metrics.get(name)!;
    metricArray.push(metric);

    // Keep only last 1000 metrics per name
    if (metricArray.length > 1000) {
      metricArray.splice(0, metricArray.length - 1000);
    }

    logger.debug('Metric recorded', { name, value, tags });
  }

  recordWebhookMetric(eventType: string, processingTime: number, success: boolean): void {
    this.recordMetric('webhook_processing_time_ms', processingTime, { 
      event_type: eventType,
      success: success.toString()
    });
    
    this.recordMetric('webhook_count', 1, { 
      event_type: eventType,
      success: success.toString()
    });
  }

  recordEmailMetric(type: string, success: boolean, processingTime?: number): void {
    this.recordMetric('email_count', 1, { 
      type,
      success: success.toString()
    });

    if (processingTime) {
      this.recordMetric('email_processing_time_ms', processingTime, { 
        type,
        success: success.toString()
      });
    }
  }

  recordSurveyResponseMetric(status: string, processingTime: number): void {
    this.recordMetric('survey_response_count', 1, { status });
    this.recordMetric('survey_response_time_ms', processingTime, { status });
  }

  recordUsageMetric(userId: string, planType: string, usagePercentage: number): void {
    this.recordMetric('usage_percentage', usagePercentage, { 
      user_id: userId,
      plan_type: planType
    });
  }

  async getHealthStatus(): Promise<HealthStatus> {
    const [dbHealth, redisHealth, stripeHealth, sendgridHealth, slackHealth] = await Promise.allSettled([
      this.checkDatabaseHealth(),
      this.checkRedisHealth(),
      this.checkStripeHealth(),
      this.checkSendGridHealth(),
      this.checkSlackHealth()
    ]);

    return {
      database: dbHealth.status === 'fulfilled' && dbHealth.value,
      redis: redisHealth.status === 'fulfilled' && redisHealth.value,
      externalServices: {
        stripe: stripeHealth.status === 'fulfilled' && stripeHealth.value,
        sendgrid: sendgridHealth.status === 'fulfilled' && sendgridHealth.value,
        slack: slackHealth.status === 'fulfilled' && slackHealth.value
      },
      uptime: Date.now() - this.startTime.getTime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage().user / 1000000 // Convert to seconds
    };
  }

  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      await database.query('SELECT 1');
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  private async checkRedisHealth(): Promise<boolean> {
    try {
      return await redisService.healthCheck();
    } catch (error) {
      logger.error('Redis health check failed:', error);
      return false;
    }
  }

  private async checkStripeHealth(): Promise<boolean> {
    try {
      // Simple health check - could be enhanced with actual API call
      return config.stripeSecretKey.length > 0;
    } catch (error) {
      logger.error('Stripe health check failed:', error);
      return false;
    }
  }

  private async checkSendGridHealth(): Promise<boolean> {
    try {
      // Simple health check - could be enhanced with actual API call
      return config.sendgridApiKey.length > 0;
    } catch (error) {
      logger.error('SendGrid health check failed:', error);
      return false;
    }
  }

  private async checkSlackHealth(): Promise<boolean> {
    try {
      // Simple health check - could be enhanced with actual API call
      return config.slackBotToken.length > 0;
    } catch (error) {
      logger.error('Slack health check failed:', error);
      return false;
    }
  }

  getMetrics(name?: string): Map<string, MetricData[]> | MetricData[] {
    if (name) {
      return this.metrics.get(name) || [];
    }
    return this.metrics;
  }

  getMetricSummary(name: string, minutes: number = 60): {
    count: number;
    average: number;
    min: number;
    max: number;
  } {
    const metrics = this.metrics.get(name);
    if (!metrics) {
      return { count: 0, average: 0, min: 0, max: 0 };
    }

    const cutoff = new Date(Date.now() - minutes * 60 * 1000);
    const recentMetrics = metrics.filter(m => m.timestamp > cutoff);

    if (recentMetrics.length === 0) {
      return { count: 0, average: 0, min: 0, max: 0 };
    }

    const values = recentMetrics.map(m => m.value);
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      count: recentMetrics.length,
      average: sum / recentMetrics.length,
      min: Math.min(...values),
      max: Math.max(...values)
    };
  }

  async generateReport(): Promise<{
    health: HealthStatus;
    metrics: Record<string, any>;
    alerts: string[];
  }> {
    const health = await this.getHealthStatus();
    const alerts: string[] = [];

    // Check for alerts
    if (!health.database) alerts.push('Database connection failed');
    if (!health.redis) alerts.push('Redis connection failed');
    if (!health.externalServices.stripe) alerts.push('Stripe service unavailable');
    if (!health.externalServices.sendgrid) alerts.push('SendGrid service unavailable');
    if (!health.externalServices.slack) alerts.push('Slack service unavailable');

    // Memory usage alert
    const memoryUsageMB = health.memoryUsage.heapUsed / 1024 / 1024;
    if (memoryUsageMB > 500) {
      alerts.push(`High memory usage: ${Math.round(memoryUsageMB)}MB`);
    }

    // Generate metrics summary
    const metrics = {
      webhooks: this.getMetricSummary('webhook_count', 60),
      emails: this.getMetricSummary('email_count', 60),
      surveyResponses: this.getMetricSummary('survey_response_count', 60),
      memoryUsage: this.getMetricSummary('memory_usage_mb', 60)
    };

    return { health, metrics, alerts };
  }

  async logPerformanceMetrics(): Promise<void> {
    const report = await this.generateReport();
    
    logger.info('Performance report', {
      uptime: Math.round(report.health.uptime / 1000 / 60), // minutes
      memoryUsageMB: Math.round(report.health.memoryUsage.heapUsed / 1024 / 1024),
      alerts: report.alerts,
      metrics: report.metrics
    });
  }
}

export default new MonitoringService(); 