import sgMail from '@sendgrid/mail';
import config from '../config';
import { EmailService, Event, Survey, SurveyResponse, User } from '../types';
import logger from '../utils/logger';
import database from './database';

class EmailServiceClass implements EmailService {
  constructor() {
    if (config.sendgridApiKey) {
      sgMail.setApiKey(config.sendgridApiKey);
    }
  }

  async sendSurveyEmail(data: {
    surveyId: string;
    customerEmail: string;
    customerName?: string;
    stripeCustomerId?: string;
    surveyTitle: string;
    whiteLabel?: boolean;
  }): Promise<{ success: boolean; emailId?: string; error?: string }> {
    try {
      if (!config.sendgridApiKey || !config.sendgridFromEmail) {
        return {
          success: false,
          error: 'SendGrid not configured'
        };
      }

      const surveyUrl = `${config.surveyBaseUrl}/exit/${data.surveyId}`;
      
      const msg = {
        to: data.customerEmail,
        from: config.sendgridFromEmail,
        subject: `We'd love your feedback`,
        text: `Hi ${data.customerName || 'there'},

We noticed you recently cancelled your subscription. We'd really appreciate if you could take a moment to share your feedback with us.

It's just one click - no forms to fill out:
${surveyUrl}

Your feedback helps us improve our service for everyone.

Thank you,
The ${data.surveyTitle} Team`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>We'd love your feedback</h2>
            <p>Hi ${data.customerName || 'there'},</p>
            <p>We noticed you recently cancelled your subscription. We'd really appreciate if you could take a moment to share your feedback with us.</p>
            <p>It's just one click - no forms to fill out:</p>
            <p style="text-align: center; margin: 30px 0;">
              <a href="${surveyUrl}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                Share Your Feedback
              </a>
            </p>
            <p>Your feedback helps us improve our service for everyone.</p>
            <p>Thank you,<br>The ${data.surveyTitle} Team</p>
            ${!data.whiteLabel ? `
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            <p style="font-size: 12px; color: #666; text-align: center;">
              Powered by <a href="https://churnbot.com" style="color: #666;">Churnbot</a>
            </p>
            ` : ''}
          </div>
        `
      };

      const response = await sgMail.send(msg);
      
      logger.info('Survey email sent successfully', {
        surveyId: data.surveyId,
        customerEmail: data.customerEmail,
        messageId: response[0]?.headers['x-message-id']
      });

      return {
        success: true,
        emailId: response[0]?.headers['x-message-id']
      };
    } catch (error) {
      logger.error('Failed to send survey email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async sendFollowUpEmail(response: SurveyResponse): Promise<void> {
    try {
      if (!config.sendgridApiKey || !config.sendgridFromEmail) {
        logger.warn('SendGrid not configured, skipping follow-up email');
        return;
      }

      // Get the event to get customer email
      const event = await database.getEventById(response.event_id);
      const customerEmail = event?.customer_email || '<EMAIL>';

      const msg = {
        to: customerEmail,
        from: config.sendgridFromEmail,
        subject: 'Quick reminder: Share your feedback',
        text: `Hi there,

Just a friendly reminder - we'd still love to hear your feedback about your experience with us.

It only takes one click:
${config.surveyBaseUrl}/exit/${response.token}

Thank you!`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Quick reminder: Share your feedback</h2>
            <p>Hi there,</p>
            <p>Just a friendly reminder - we'd still love to hear your feedback about your experience with us.</p>
            <p style="text-align: center; margin: 30px 0;">
              <a href="${config.surveyBaseUrl}/exit/${response.token}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                Share Your Feedback
              </a>
            </p>
            <p>Thank you!</p>
          </div>
        `
      };

      await sgMail.send(msg);
      
      logger.info('Follow-up email sent', {
        responseId: response.id,
        customerEmail: customerEmail
      });
    } catch (error) {
      logger.error('Failed to send follow-up email:', error);
    }
  }

  async sendWelcomeEmail(user: User): Promise<void> {
    try {
      if (!config.sendgridApiKey || !config.sendgridFromEmail) {
        logger.warn('SendGrid not configured, skipping welcome email');
        return;
      }

      const msg = {
        to: user.email,
        from: config.sendgridFromEmail,
        subject: 'Welcome to Churn-Exit Survey Bot!',
        text: `Hi ${user.name || 'there'},

Welcome to Churn-Exit Survey Bot! We're excited to help you gather valuable feedback from your customers.

To get started:
1. Connect your Stripe account
2. Create your first survey
3. Set up your Customer Portal redirect URL

If you need any help, just reply to this email.

Best regards,
The Churn-Exit Survey Bot Team`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Welcome to Churn-Exit Survey Bot!</h2>
            <p>Hi ${user.name || 'there'},</p>
            <p>Welcome to Churn-Exit Survey Bot! We're excited to help you gather valuable feedback from your customers.</p>
            <h3>To get started:</h3>
            <ol>
              <li>Connect your Stripe account</li>
              <li>Create your first survey</li>
              <li>Set up your Customer Portal redirect URL</li>
            </ol>
            <p>If you need any help, just reply to this email.</p>
            <p>Best regards,<br>The Churn-Exit Survey Bot Team</p>
          </div>
        `
      };

      await sgMail.send(msg);
      
      logger.info('Welcome email sent', {
        userId: user.id,
        email: user.email
      });
    } catch (error) {
      logger.error('Failed to send welcome email:', error);
    }
  }
}

export default new EmailServiceClass(); 