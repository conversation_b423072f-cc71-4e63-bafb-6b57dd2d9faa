import { format } from 'date-fns';
import database from './database';
import { UsageService, PLAN_LIMITS } from '../types';
import logger from '../utils/logger';

class UsageServiceClass implements UsageService {
  async checkUsageLimits(userId: string): Promise<{
    current: number;
    limit: number;
    percentage: number;
  }> {
    const user = await database.getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const currentMonth = format(new Date(), 'yyyy-MM');
    let usage = await database.getUsageLog(userId, currentMonth);
    
    if (!usage) {
      usage = await database.createUsageLog(userId, currentMonth);
    }

    const limit = PLAN_LIMITS[user.plan_type];
    const percentage = (usage.events_count / limit) * 100;

    logger.debug('Usage check', {
      userId,
      current: usage.events_count,
      limit,
      percentage: Math.round(percentage)
    });

    return {
      current: usage.events_count,
      limit,
      percentage
    };
  }

  async incrementUsage(userId: string): Promise<void> {
    const currentMonth = format(new Date(), 'yyyy-MM');
    let usage = await database.getUsageLog(userId, currentMonth);
    
    if (!usage) {
      usage = await database.createUsageLog(userId, currentMonth);
    }

    await database.incrementUsage(userId, currentMonth);

    logger.info('Usage incremented', {
      userId,
      month: currentMonth,
      newCount: usage.events_count + 1
    });
  }

  async getUsageHistory(userId: string, months: number = 6): Promise<any[]> {
    const history = [];
    const currentDate = new Date();

    for (let i = 0; i < months; i++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthYear = format(date, 'yyyy-MM');
      
      const usage = await database.getUsageLog(userId, monthYear);
      if (usage) {
        history.push({
          month: monthYear,
          events_count: usage.events_count,
          created_at: usage.created_at
        });
      }
    }

    return history.reverse();
  }

  async isOverLimit(userId: string): Promise<boolean> {
    const usage = await this.checkUsageLimits(userId);
    return usage.current >= usage.limit;
  }

  async getUpgradePrompt(userId: string): Promise<{
    shouldShow: boolean;
    message: string;
    percentage: number;
  } | null> {
    const usage = await this.checkUsageLimits(userId);
    const user = await database.getUserById(userId);

    if (!user) return null;

    // Show upgrade prompt at 80% usage for free plan
    if (user.plan_type === 'free' && usage.percentage >= 80) {
      return {
        shouldShow: true,
        message: `You've used ${Math.round(usage.percentage)}% of this month's free quota. Upgrade to keep insights flowing.`,
        percentage: usage.percentage
      };
    }

    // Show upgrade prompt at 90% usage for pro plan
    if (user.plan_type === 'pro' && usage.percentage >= 90) {
      return {
        shouldShow: true,
        message: `You're approaching Pro limits. Scale plan ($49 unlimited) keeps you covered.`,
        percentage: usage.percentage
      };
    }

    return {
      shouldShow: false,
      message: '',
      percentage: usage.percentage
    };
  }
}

export default new UsageServiceClass(); 