import { WebClient } from '@slack/web-api';
import config from '../config';
import database from './database';
import { SurveyResponse } from '../types';
import logger from '../utils/logger';

class SlackServiceSimple {
  private client: WebClient;

  constructor() {
    this.client = new WebClient(config.slackBotToken);
  }

  async sendNotification(response: SurveyResponse): Promise<void> {
    try {
      const event = await database.getEventById(response.event_id);
      const survey = await database.getSurveyById(response.survey_id);
      
      if (!event || !survey) {
        throw new Error('Event or survey not found for Slack notification');
      }

      const emoji = this.getEmoji(response.selected_option);
      const message = {
        channel: survey.slack_channel_id,
        text: `${emoji} Churn – ${response.selected_option} – ${event.customer_name} – $${event.mrr_lost} MRR`,
        attachments: [{
          fields: [
            {
              title: 'Customer',
              value: event.customer_email || 'Unknown',
              short: true
            },
            {
              title: 'Plan',
              value: event.plan_name,
              short: true
            },
            {
              title: 'Reason',
              value: response.selected_option,
              short: true
            }
          ]
        }]
      };

      // @ts-ignore - Bypass strict typing for Slack API
      await this.client.chat.postMessage(message);

      logger.info('Slack notification sent', {
        responseId: response.id,
        channel: survey.slack_channel_id,
        reason: response.selected_option
      });
    } catch (error) {
      logger.error('Failed to send Slack notification:', error);
      throw error;
    }
  }

  async sendNoResponseNotification(response: SurveyResponse): Promise<void> {
    try {
      const event = await database.getEventById(response.event_id);
      const survey = await database.getSurveyById(response.survey_id);
      
      if (!event || !survey) {
        throw new Error('Event or survey not found for Slack notification');
      }

      let statusText = 'No response';
      if (response.email_opened_at) {
        statusText = `No response – email opened ${response.open_count}×`;
      }

      const message = {
        channel: survey.slack_channel_id,
        text: `❓ Churn – ${statusText} – ${event.customer_name} – $${event.mrr_lost} MRR`,
        attachments: [{
          fields: [
            {
              title: 'Customer',
              value: event.customer_email || 'Unknown',
              short: true
            },
            {
              title: 'Plan',
              value: event.plan_name,
              short: true
            },
            {
              title: 'Status',
              value: statusText,
              short: true
            }
          ]
        }]
      };

      // @ts-ignore - Bypass strict typing for Slack API
      await this.client.chat.postMessage(message);

      logger.info('Slack no-response notification sent', {
        responseId: response.id,
        channel: survey.slack_channel_id,
        status: statusText
      });
    } catch (error) {
      logger.error('Failed to send Slack no-response notification:', error);
      throw error;
    }
  }

  private getEmoji(reason: string): string {
    const emojiMap: Record<string, string> = {
      'too_expensive': '💰',
      'missing_features': '🔧',
      'not_using': '📊',
      'switched': '🔄',
      'customer_service': '💬',
      'technical_issues': '🐛',
      'other': '❓'
    };
    return emojiMap[reason] || '❓';
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.client.auth.test();
      return true;
    } catch (error) {
      logger.error('Slack connection test failed:', error);
      return false;
    }
  }
}

export default new SlackServiceSimple(); 