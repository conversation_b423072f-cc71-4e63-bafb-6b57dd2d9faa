import Stripe from 'stripe';
import database from './database';
import logger from '../utils/logger';
import config from '../config';

interface PlanLimits {
  free: { events: 25; features: string[] };
  pro: { events: 500; features: string[] };
  scale: { events: -1; features: string[] }; // -1 means unlimited
}

const PLAN_LIMITS: PlanLimits = {
  free: {
    events: 25,
    features: [
      'basic_surveys',
      'response_viewer',
      'email_tracking',
    ],
  },
  pro: {
    events: 500,
    features: [
      'basic_surveys',
      'response_viewer',
      'email_tracking',
      'white_label_email',   // no branding
      'advanced_filters',
    ],
  },
  scale: {
    events: -1,
    features: [
      'basic_surveys',
      'response_viewer',
      'email_tracking',
      'white_label_email',
      'advanced_filters',
      'webhook_out',
      'csv_export',
    ],
  },
};

class BillingService {
  private stripe: Stripe;

  constructor() {
    this.stripe = new Stripe(config.stripeSecretKey, {
      apiVersion: '2023-10-16'
    });
  }

  async createCheckoutSession(userId: string, planType: 'pro' | 'scale'): Promise<Stripe.Checkout.Session> {
    const priceId = planType === 'pro' ? config.stripeProPriceId : config.stripeScalePriceId;
    
    const session = await this.stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${config.dashboardUrl}/billing/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${config.dashboardUrl}/billing/cancel`,
      client_reference_id: userId,
      metadata: {
        userId,
        planType
      }
    });

    logger.info('Checkout session created', { 
      sessionId: session.id, 
      userId, 
      planType 
    });

    return session;
  }

  async createCustomerPortalSession(userId: string): Promise<Stripe.BillingPortal.Session> {
    const user = await database.getUserById(userId);
    if (!user || !user.stripe_customer_id) {
      throw new Error('User not found or no Stripe customer ID');
    }

    const session = await this.stripe.billingPortal.sessions.create({
      customer: user.stripe_customer_id,
      return_url: `${config.dashboardUrl}/dashboard`,
    });

    logger.info('Customer portal session created', { 
      sessionId: session.id, 
      userId 
    });

    return session;
  }

  async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    const userId = subscription.metadata.userId;
    if (!userId) {
      logger.warn('Subscription missing userId metadata', { subscriptionId: subscription.id });
      return;
    }

    const user = await database.getUserById(userId);
    if (!user) {
      logger.warn('User not found for subscription update', { userId, subscriptionId: subscription.id });
      return;
    }

    let newPlanType: 'free' | 'pro' | 'scale' = 'free';

    // Determine plan type based on subscription
    if (subscription.status === 'active') {
      const priceId = subscription.items.data[0]?.price.id;
      if (priceId === config.stripeProPriceId) {
        newPlanType = 'pro';
      } else if (priceId === config.stripeScalePriceId) {
        newPlanType = 'scale';
      }
    }

    // Update user plan
    await database.updateUser(userId, {
      plan_type: newPlanType,
      updated_at: new Date()
    });

    logger.info('User plan updated', { 
      userId, 
      oldPlan: user.plan_type, 
      newPlan: newPlanType,
      subscriptionId: subscription.id 
    });
  }

  async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    const userId = subscription.metadata.userId;
    if (!userId) {
      logger.warn('Subscription missing userId metadata', { subscriptionId: subscription.id });
      return;
    }

    // Downgrade to free plan
    await database.updateUser(userId, {
      plan_type: 'free',
      updated_at: new Date()
    });

    logger.info('User downgraded to free plan', { 
      userId, 
      subscriptionId: subscription.id 
    });
  }

  async checkUsageLimits(userId: string): Promise<{
    current: number;
    limit: number;
    percentage: number;
    planType: string;
    canProcess: boolean;
  }> {
    const user = await database.getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
    const usageLog = await database.getUsageLog(userId, currentMonth);
    
    const currentEvents = usageLog ? usageLog.events_count : 0;
    const planLimit = PLAN_LIMITS[user.plan_type].events;
    const percentage = planLimit === -1 ? 0 : Math.round((currentEvents / planLimit) * 100);
    const canProcess = planLimit === -1 || currentEvents < planLimit;

    return {
      current: currentEvents,
      limit: planLimit,
      percentage,
      planType: user.plan_type,
      canProcess
    };
  }

  async incrementUsage(userId: string): Promise<void> {
    const currentMonth = new Date().toISOString().slice(0, 7);
    
    // Get or create usage log
    let usageLog = await database.getUsageLog(userId, currentMonth);
    if (!usageLog) {
      usageLog = await database.createUsageLog(userId, currentMonth);
    }

    // Check limits before incrementing
    const limits = await this.checkUsageLimits(userId);
    if (!limits.canProcess) {
      throw new Error(`Usage limit exceeded for ${limits.planType} plan`);
    }

    await database.incrementUsage(userId, currentMonth);
    
    logger.info('Usage incremented', { 
      userId, 
      month: currentMonth, 
      newCount: limits.current + 1 
    });
  }

  async getPlanFeatures(planType: 'free' | 'pro' | 'scale'): Promise<string[]> {
    return PLAN_LIMITS[planType].features;
  }

  async hasFeature(userId: string, feature: string): Promise<boolean> {
    const user = await database.getUserById(userId);
    if (!user) return false;

    const features = await this.getPlanFeatures(user.plan_type);
    return features.includes(feature);
  }
}

export default new BillingService(); 