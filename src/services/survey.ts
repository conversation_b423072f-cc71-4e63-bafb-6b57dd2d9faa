import { v4 as uuidv4 } from 'uuid';
import database from './database';
import logger from '../utils/logger';
import config from '../config';
import { SurveyResponse } from '../types';

interface CreateSurveyData {
  userId: string;
  title: string;
  questions: any[];
  branding?: any;
  followUpEnabled?: boolean;
  trackingEnabled?: boolean;
}

interface UpdateSurveyData {
  title?: string;
  questions?: any[];
  branding?: any;
  followUpEnabled?: boolean;
  trackingEnabled?: boolean;
}

interface GenerateUrlData {
  customerEmail: string;
  customerName?: string;
  stripeCustomerId?: string;
}

class SurveyService {
  async createSurvey(data: CreateSurveyData) {
    const surveyId = uuidv4();
    
    const survey = await database.createSurvey({
      id: surveyId,
      user_id: data.userId,
      question_text: data.title,
      options: data.questions,
      branding: data.branding || {},
      slack_channel_id: 'default-channel',
      slack_workspace_id: 'default-workspace',
      follow_up_enabled: data.followUpEnabled || false,
      tracking_enabled: data.trackingEnabled || false,
      created_at: new Date()
    });

    return survey;
  }

  async updateSurvey(id: string, data: UpdateSurveyData) {
    const updates: any = {
      updated_at: new Date()
    };

    if (data.title) updates.title = data.title;
    if (data.questions) updates.questions = JSON.stringify(data.questions);
    if (data.branding) updates.branding = JSON.stringify(data.branding);
    if (data.followUpEnabled !== undefined) updates.follow_up_enabled = data.followUpEnabled;
    if (data.trackingEnabled !== undefined) updates.tracking_enabled = data.trackingEnabled;

    const survey = await database.updateSurvey(id, updates);
    return survey;
  }

  async deleteSurvey(id: string) {
    // First delete all responses for this survey
    await database.deleteResponsesBySurveyId(id);
    
    // Then delete the survey
    await database.deleteSurvey(id);
  }

  async generateSurveyUrl(surveyId: string, data: GenerateUrlData) {
    // Create a unique token for this survey response
    const token = uuidv4();
    
    // Create a dummy event for this survey response
    const dummyEvent = await database.createEvent({
      user_id: 'a024e311-1321-4974-a04a-f870442218cb', // Use the test user for now
      stripe_event_id: `dummy-${Date.now()}`,
      event_type: 'customer.subscription.deleted',
      customer_id: data.stripeCustomerId || 'dummy-customer',
      subscription_id: 'dummy-subscription',
      plan_name: 'Test Plan',
      mrr_lost: 0,
      canceled_at: new Date(),
      customer_email: data.customerEmail,
      customer_name: data.customerName
    });
    
    // Store the survey response record
    await database.createResponse({
      event_id: dummyEvent.id,
      survey_id: surveyId,
      token: token,
      status: 'pending',
      open_count: 0
    });

    // Generate the survey URL
    const surveyUrl = `${config.surveyBaseUrl}/exit/${token}`;
    
    return surveyUrl;
  }

  async getSurveyResponses(surveyId: string, options: {
    page?: number;
    limit?: number;
    status?: string;
  } = {}) {
    return await database.getResponsesBySurveyId(surveyId, options);
  }

  async processSurveyResponse(token: string, response: any) {
    const surveyResponse = await database.getResponseByToken(token);
    
    if (!surveyResponse) {
      throw new Error('Invalid survey token');
    }

    if (surveyResponse.status !== 'pending') {
      throw new Error('Survey already completed');
    }

    // Update the response with the survey answers
    const updatedResponse = await database.updateResponse(surveyResponse.id, {
      selected_option: response.selected_option,
      custom_text: response.custom_text,
      status: 'responded',
      responded_at: new Date()
    });

    // Get the survey details
    const survey = await database.getSurveyById(surveyResponse.survey_id);
    if (!survey) {
      throw new Error('Survey not found');
    }

    // Send Slack notification if configured
    if (survey.slack_channel_id) {
      // This would be handled by the job processor
      logger.info('Survey response completed, Slack notification queued', {
        surveyId: surveyResponse.survey_id
      });
    }

    return updatedResponse;
  }

  async getResponseByToken(token: string): Promise<SurveyResponse | null> {
    return await database.getResponseByToken(token);
  }

  async updateResponse(token: string, data: Partial<SurveyResponse>): Promise<void> {
    const response = await database.getResponseByToken(token);
    if (!response) {
      throw new Error('Response not found');
    }
    await database.updateResponse(response.id, data);
  }

  async markAsNoResponse(responseId: string): Promise<void> {
    await database.updateResponse(responseId, {
      status: 'no_response',
      no_response_at: new Date()
    });
  }

  async getPendingResponses(hoursOld: number): Promise<SurveyResponse[]> {
    return await database.getPendingResponses(hoursOld);
  }

  generateToken(): string {
    return uuidv4();
  }

  async createResponse(eventId: string, surveyId: string): Promise<SurveyResponse> {
    const token = this.generateToken();
    
    const response = await database.createResponse({
      event_id: eventId,
      survey_id: surveyId,
      token: token,
      status: 'pending',
      open_count: 0
    });

    logger.info('Survey response created', {
      eventId,
      surveyId,
      token: token.substring(0, 8) + '...' // Log partial token for security
    });

    return response;
  }
}

export default new SurveyService(); 