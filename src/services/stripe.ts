import Stripe from 'stripe';
import config from '../config';
import { StripeService, StripeSubscription } from '../types';
import logger from '../utils/logger';

class StripeServiceClass implements StripeService {
  private stripe: Stripe;

  constructor() {
    this.stripe = new Stripe(config.stripeSecretKey, {
      apiVersion: '2023-10-16',
    });
  }

  verifyWebhookSignature(payload: string, signature: string): Stripe.Event {
    try {
      return this.stripe.webhooks.constructEvent(
        payload,
        signature,
        config.stripeWebhookSecret
      );
    } catch (error) {
      logger.error('Webhook signature verification failed:', error);
      throw new Error('Invalid webhook signature');
    }
  }

  async getCustomer(customerId: string): Promise<Stripe.Customer> {
    try {
      return await this.stripe.customers.retrieve(customerId) as Stripe.Customer;
    } catch (error) {
      logger.error('Failed to retrieve customer:', { customerId, error });
      throw error;
    }
  }

  async getSubscription(subscriptionId: string): Promise<StripeSubscription> {
    try {
      return await this.stripe.subscriptions.retrieve(subscriptionId) as StripeSubscription;
    } catch (error) {
      logger.error('Failed to retrieve subscription:', { subscriptionId, error });
      throw error;
    }
  }

  async createCheckoutSession(userId: string, planType: string): Promise<Stripe.Checkout.Session> {
    try {
      const priceIds = {
        pro: process.env.STRIPE_PRO_PRICE_ID,
        scale: process.env.STRIPE_SCALE_PRICE_ID
      };

      const priceId = priceIds[planType as keyof typeof priceIds];
      if (!priceId) {
        throw new Error(`Invalid plan type: ${planType}`);
      }

      return await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [{
          price: priceId,
          quantity: 1,
        }],
        mode: 'subscription',
        success_url: `${config.dashboardUrl}/upgrade/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${config.dashboardUrl}/upgrade/cancel`,
        metadata: {
          user_id: userId,
          plan_type: planType
        }
      });
    } catch (error) {
      logger.error('Failed to create checkout session:', { userId, planType, error });
      throw error;
    }
  }

  async createWebhookEndpoint(userId: string, uid: string): Promise<Stripe.WebhookEndpoint> {
    try {
      return await this.stripe.webhookEndpoints.create({
        url: `${config.apiBaseUrl}/webhooks/${uid}`,
        enabled_events: [
          'customer.subscription.deleted',
          'customer.subscription.updated'
        ],
        metadata: {
          user_id: userId,
          uid: uid
        }
      });
    } catch (error) {
      logger.error('Failed to create webhook endpoint:', { userId, uid, error });
      throw error;
    }
  }

  async deleteWebhookEndpoint(endpointId: string): Promise<void> {
    try {
      await this.stripe.webhookEndpoints.del(endpointId);
    } catch (error) {
      logger.error('Failed to delete webhook endpoint:', { endpointId, error });
      throw error;
    }
  }

  calculateMRR(subscription: StripeSubscription): number {
    try {
      const item = subscription.items.data[0];
      if (!item || !item.price) {
        return 0;
      }

      const price = item.price;
      if (price.type === 'recurring' && price.unit_amount) {
        return price.unit_amount / 100; // Convert from cents
      }

      return 0;
    } catch (error) {
      logger.error('Failed to calculate MRR:', { subscriptionId: subscription.id, error });
      return 0;
    }
  }

  async getCustomerPortalSession(customerId: string, returnUrl: string): Promise<Stripe.BillingPortal.Session> {
    try {
      return await this.stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      });
    } catch (error) {
      logger.error('Failed to create customer portal session:', { customerId, returnUrl, error });
      throw error;
    }
  }

  // OAuth Methods
  async exchangeCodeForToken(code: string): Promise<{
    success: boolean;
    accessToken?: string;
    refreshToken?: string;
    error?: string;
  }> {
    try {
      const response = await fetch('https://connect.stripe.com/oauth/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          client_id: config.stripeClientId,
          client_secret: config.stripeSecretKey,
          code,
        }),
      });

      const data = await response.json() as any;

      if (!response.ok) {
        return {
          success: false,
          error: data.error_description || 'Failed to exchange code for token'
        };
      }

      return {
        success: true,
        accessToken: data.access_token,
        refreshToken: data.refresh_token
      };
    } catch (error) {
      logger.error('Failed to exchange Stripe code for token:', error);
      return {
        success: false,
        error: 'Token exchange failed'
      };
    }
  }

  async getAccountDetails(accessToken: string): Promise<{
    success: boolean;
    account?: {
      id: string;
      email: string;
      business_type: string;
      country: string;
    };
    error?: string;
  }> {
    try {
      const response = await fetch('https://api.stripe.com/v1/account', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const data = await response.json() as any;

      if (!response.ok) {
        return {
          success: false,
          error: data.error?.message || 'Failed to get account details'
        };
      }

      return {
        success: true,
        account: {
          id: data.id,
          email: data.email,
          business_type: data.business_type,
          country: data.country
        }
      };
    } catch (error) {
      logger.error('Failed to get Stripe account details:', error);
      return {
        success: false,
        error: 'Failed to get account details'
      };
    }
  }

  async exchangeSlackCodeForToken(code: string): Promise<{
    success: boolean;
    accessToken?: string;
    botToken?: string;
    error?: string;
  }> {
    try {
      const response = await fetch('https://slack.com/api/oauth.v2.access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: config.slackClientId,
          client_secret: config.slackClientSecret,
          code,
          redirect_uri: config.slackRedirectUri,
        }),
      });

      const data = await response.json() as any;

      if (!data.ok) {
        return {
          success: false,
          error: data.error || 'Failed to exchange code for token'
        };
      }

      return {
        success: true,
        accessToken: data.access_token,
        botToken: data.bot?.bot_access_token
      };
    } catch (error) {
      logger.error('Failed to exchange Slack code for token:', error);
      return {
        success: false,
        error: 'Token exchange failed'
      };
    }
  }

  async getSlackWorkspaceDetails(accessToken: string): Promise<{
    success: boolean;
    workspace?: {
      id: string;
      name: string;
    };
    error?: string;
  }> {
    try {
      const response = await fetch('https://slack.com/api/team.info', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json() as any;

      if (!data.ok) {
        return {
          success: false,
          error: data.error || 'Failed to get workspace details'
        };
      }

      return {
        success: true,
        workspace: {
          id: data.team.id,
          name: data.team.name
        }
      };
    } catch (error) {
      logger.error('Failed to get Slack workspace details:', error);
      return {
        success: false,
        error: 'Failed to get workspace details'
      };
    }
  }

  generateJWT(userId: string): string {
    const jwt = require('jsonwebtoken');
    return jwt.sign({ userId }, config.jwtSecret, { expiresIn: config.jwtExpiresIn });
  }
}

export default new StripeServiceClass(); 