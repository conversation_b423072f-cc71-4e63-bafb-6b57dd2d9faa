import { sql } from 'drizzle-orm';
import { getDb, closeDb } from '../db/client';
import { DatabaseService, User, Survey, Event, SurveyResponse, UsageLog } from '../types';
import logger from '../utils/logger';


class Database implements DatabaseService {

  async connect(): Promise<void> {
    try {
      // Ensure client initializes and a simple query succeeds
      const db = getDb();
      await db.execute(sql`select now()`);
      logger.info('Database connected successfully (Drizzle + pg)');
    } catch (error) {
      logger.error('Database connection failed:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    await closeDb();
    logger.info('Database disconnected');
  }

  // For legacy raw SQL usage where needed
  async query<T = any>(text: string, params?: any[]): Promise<{ rows: T[] }> {
    // Use node-postgres client directly for parameterized raw SQL
    const pool = (await import('../db/client')).getPool();
    const start = Date.now();
    try {
      const res = await pool.query(text, params ?? []);
      const duration = Date.now() - start;
      if (duration > 1000) {
        logger.warn('Slow query detected', { text: text.substring(0, 100) + '...', duration, rows: res.rowCount });
      }
      logger.debug('Executed query', { text: text.substring(0, 100) + '...', duration, rows: res.rowCount });
      return { rows: res.rows as T[] };
    } catch (error) {
      logger.error('Query error:', {
        text: text.substring(0, 100) + '...',
        params: params?.map(p => typeof p === 'string' ? p.substring(0, 50) + '...' : p),
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  // The rest of the methods remain using raw SQL for now to avoid a large refactor.
  // They will operate via this.query which delegates to Drizzle/pg.

  // User methods
  async createUser(userData: Partial<User>): Promise<User> {
    const { rows } = await this.query<User>(
      `INSERT INTO users (uid, stripe_customer_id, stripe_account_id, email, plan_type)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING *`,
      [userData.uid, userData.stripe_customer_id, userData.stripe_account_id, userData.email, userData.plan_type || 'free']
    );
    if (!rows[0]) {
      throw new Error('Failed to create user');
    }
    return rows[0];
  }

  async getUserById(id: string): Promise<User | null> {
    const { rows } = await this.query<User>('SELECT * FROM users WHERE id = $1', [id]);
    return rows[0] || null;
  }

  async getUserByUid(uid: string): Promise<User | null> {
    const { rows } = await this.query<User>('SELECT * FROM users WHERE uid = $1', [uid]);
    return rows[0] || null;
  }

  async getUserByStripeCustomerId(stripeCustomerId: string): Promise<User | null> {
    const { rows } = await this.query<User>('SELECT * FROM users WHERE stripe_customer_id = $1', [stripeCustomerId]);
    return rows[0] || null;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    const fields = Object.keys(updates).map((key, index) => `${key} = $${index + 2}`).join(', ');
    const values = Object.values(updates);
    const { rows } = await this.query<User>(
      `UPDATE users SET ${fields}, updated_at = NOW() WHERE id = $1 RETURNING *`,
      [id, ...values]
    );
    if (!rows[0]) throw new Error('User not found');
    return rows[0];
  }

  // Survey methods
  async createSurvey(surveyData: Partial<Survey>): Promise<Survey> {
    const { rows } = await this.query<Survey>(
      `INSERT INTO surveys (user_id, question_text, options, branding, slack_channel_id,
        slack_workspace_id, follow_up_enabled, tracking_enabled, is_active)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
       RETURNING *`,
      [
        surveyData.user_id,
        surveyData.question_text,
        JSON.stringify(surveyData.options),
        JSON.stringify(surveyData.branding),
        surveyData.slack_channel_id,
        surveyData.slack_workspace_id,
        surveyData.follow_up_enabled || false,
        surveyData.tracking_enabled || false,
        surveyData.is_active !== false
      ]
    );
    if (!rows[0]) throw new Error('Failed to create survey');
    return rows[0];
  }

  async getActiveSurvey(userId: string): Promise<Survey | null> {
    const { rows } = await this.query<Survey>(
      'SELECT * FROM surveys WHERE user_id = $1 AND is_active = true ORDER BY created_at DESC LIMIT 1',
      [userId]
    );
    return rows[0] || null;
  }

  async getSurveysByUserId(userId: string): Promise<Survey[]> {
    const { rows } = await this.query<Survey>(
      'SELECT * FROM surveys WHERE user_id = $1 ORDER BY created_at DESC',
      [userId]
    );
    return rows;
  }

  async updateSurvey(id: string, updates: Partial<Survey>): Promise<Survey> {
    const setClause = Object.keys(updates)
      .map((key, index) => `${key} = $${index + 2}`)
      .join(', ');
    
    const values = [id, ...Object.values(updates)];
    
    const { rows } = await this.query<Survey>(
      `UPDATE surveys SET ${setClause} WHERE id = $1 RETURNING *`,
      values
    );
    
    if (!rows[0]) {
      throw new Error('Survey not found');
    }
    
    return rows[0];
  }

  async deleteSurvey(id: string): Promise<void> {
    await this.query('DELETE FROM surveys WHERE id = $1', [id]);
  }

  async deleteResponsesBySurveyId(surveyId: string): Promise<void> {
    await this.query('DELETE FROM responses WHERE survey_id = $1', [surveyId]);
  }

  async getResponsesBySurveyId(surveyId: string, options: {
    page?: number;
    limit?: number;
    status?: string;
  } = {}): Promise<SurveyResponse[]> {
    const { page = 1, limit = 50, status } = options;
    const offset = (page - 1) * limit;
    
    let query = 'SELECT * FROM responses WHERE survey_id = $1';
    const params: any[] = [surveyId];
    
    if (status) {
      query += ' AND status = $2';
      params.push(status);
    }
    
    query += ' ORDER BY created_at DESC LIMIT $' + (params.length + 1) + ' OFFSET $' + (params.length + 2);
    params.push(limit, offset);
    
    const { rows } = await this.query<SurveyResponse>(query, params);
    return rows;
  }

  async getSurveyById(id: string): Promise<Survey | null> {
    const { rows } = await this.query<Survey>('SELECT * FROM surveys WHERE id = $1', [id]);
    return rows[0] || null;
  }

  // Event methods
  async createEvent(eventData: Partial<Event>): Promise<Event> {
    const { rows } = await this.query<Event>(
      `INSERT INTO events (user_id, stripe_event_id, event_type, customer_id, subscription_id,
        plan_name, mrr_lost, canceled_at, processed_at, customer_email, customer_name)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), $9, $10)
       RETURNING *`,
      [
        eventData.user_id,
        eventData.stripe_event_id,
        eventData.event_type,
        eventData.customer_id,
        eventData.subscription_id,
        eventData.plan_name,
        eventData.mrr_lost,
        eventData.canceled_at,
        eventData.customer_email,
        eventData.customer_name
      ]
    );
    return rows[0];
  }

  async getEventById(id: string): Promise<Event | null> {
    const { rows } = await this.query<Event>('SELECT * FROM events WHERE id = $1', [id]);
    return rows[0] || null;
  }

  async getEventsByUserId(userId: string, limit = 50, offset = 0): Promise<Event[]> {
    const { rows } = await this.query<Event>(
      'SELECT * FROM events WHERE user_id = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3',
      [userId, limit, offset]
    );
    return rows;
  }

  // Response methods
  async createResponse(responseData: Partial<SurveyResponse>): Promise<SurveyResponse> {
    const { rows } = await this.query<SurveyResponse>(
      `INSERT INTO responses (event_id, survey_id, token, status, open_count)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING *`,
      [
        responseData.event_id,
        responseData.survey_id,
        responseData.token,
        responseData.status || 'pending',
        responseData.open_count || 0
      ]
    );
    return rows[0];
  }

  async getResponseByToken(token: string): Promise<SurveyResponse | null> {
    const { rows } = await this.query<SurveyResponse>(
      'SELECT * FROM responses WHERE token = $1',
      [token]
    );
    return rows[0] || null;
  }

  async updateResponse(id: string, updates: Partial<SurveyResponse>): Promise<SurveyResponse> {
    const setClause = Object.keys(updates)
      .map((key, index) => `${key} = $${index + 2}`)
      .join(', ');
    
    const values = [id, ...Object.values(updates)];
    
    const { rows } = await this.query<SurveyResponse>(
      `UPDATE responses SET ${setClause} WHERE id = $1 RETURNING *`,
      values
    );
    
    if (!rows[0]) {
      throw new Error('Response not found');
    }
    
    return rows[0];
  }



  async getPendingResponses(hoursOld: number): Promise<SurveyResponse[]> {
    const { rows } = await this.query<SurveyResponse>(
      `SELECT r.*, s.follow_up_enabled
       FROM responses r
       JOIN surveys s ON r.survey_id = s.id
       WHERE r.status = 'pending'
       AND r.created_at < NOW() - INTERVAL '${hoursOld} hours'
       AND r.follow_up_sent_at IS NULL`
    );
    return rows;
  }

  // Usage methods
  async getUsageLog(userId: string, month: string): Promise<any> {
    try {
      const { rows } = await this.query<any>(
        'SELECT * FROM usage_logs WHERE user_id = $1 AND month_year = $2',
        [userId, month]
      );
      return rows[0] || null;
    } catch (error) {
      logger.error('Failed to get usage log:', error);
      throw error;
    }
  }

  async createUsageLog(userId: string, month: string): Promise<any> {
    try {
      const { rows } = await this.query<any>(
        `INSERT INTO usage_logs (user_id, month_year, events_count)
         VALUES ($1, $2, 0)
         RETURNING *`,
        [userId, month]
      );
      return rows[0];
    } catch (error) {
      logger.error('Failed to create usage log:', error);
      throw error;
    }
  }

  async incrementUsage(userId: string, month: string): Promise<void> {
    try {
      await this.query(
        `UPDATE usage_logs
         SET events_count = events_count + 1
         WHERE user_id = $1 AND month_year = $2`,
        [userId, month]
      );
    } catch (error) {
      logger.error('Failed to increment usage:', error);
      throw error;
    }
  }

  async getUsageLogsByUserId(userId: string): Promise<any[]> {
    try {
      const { rows } = await this.query<any>(
        'SELECT * FROM usage_logs WHERE user_id = $1 ORDER BY month_year DESC',
        [userId]
      );
      return rows;
    } catch (error) {
      logger.error('Failed to get usage logs by user ID:', error);
      throw error;
    }
  }
}

export default new Database();