import Redis from 'ioredis';
import config from '../config';
import logger from '../utils/logger';

class RedisService {
  private client: Redis;
  private subscriber: Redis;

  constructor() {
    this.client = new Redis(config.redisUrl, {
      lazyConnect: true,
    });

    this.subscriber = new Redis(config.redisUrl, {
      lazyConnect: true,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.client.on('connect', () => {
      logger.info('Redis client connected');
    });

    this.client.on('error', (error) => {
      logger.error('Redis client error:', error);
    });

    this.client.on('close', () => {
      logger.warn('Redis client connection closed');
    });

    this.subscriber.on('connect', () => {
      logger.info('Redis subscriber connected');
    });

    this.subscriber.on('error', (error) => {
      logger.error('Redis subscriber error:', error);
    });
  }

  async connect(): Promise<void> {
    try {
      await this.client.ping();
      await this.subscriber.ping();
      logger.info('Redis connected successfully');
    } catch (error) {
      logger.error('Redis connection failed:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.client.quit();
      await this.subscriber.quit();
      logger.info('Redis disconnected');
    } catch (error) {
      logger.error('Redis disconnect error:', error);
    }
  }

  // Cache operations
  async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(key);
    } catch (error) {
      logger.error('Redis get error:', { key, error });
      return null;
    }
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        await this.client.setex(key, ttl, value);
      } else {
        await this.client.set(key, value);
      }
    } catch (error) {
      logger.error('Redis set error:', { key, error });
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error) {
      logger.error('Redis del error:', { key, error });
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis exists error:', { key, error });
      return false;
    }
  }

  // Rate limiting
  async incrementRateLimit(key: string, window: number): Promise<number> {
    try {
      const multi = this.client.multi();
      multi.incr(key);
      multi.expire(key, window);
      const results = await multi.exec();
      return results?.[0]?.[1] as number || 0;
    } catch (error) {
      logger.error('Redis rate limit error:', { key, error });
      return 0;
    }
  }

  // Job queue operations
  async enqueueJob(queue: string, job: any): Promise<void> {
    try {
      await this.client.lpush(queue, JSON.stringify(job));
    } catch (error) {
      logger.error('Redis enqueue error:', { queue, error });
    }
  }

  async dequeueJob(queue: string): Promise<any | null> {
    try {
      const job = await this.client.rpop(queue);
      return job ? JSON.parse(job) : null;
    } catch (error) {
      logger.error('Redis dequeue error:', { queue, error });
      return null;
    }
  }

  // Session management
  async setSession(sessionId: string, data: any, ttl: number = 3600): Promise<void> {
    try {
      await this.client.setex(`session:${sessionId}`, ttl, JSON.stringify(data));
    } catch (error) {
      logger.error('Redis set session error:', { sessionId, error });
    }
  }

  async getSession(sessionId: string): Promise<any | null> {
    try {
      const data = await this.client.get(`session:${sessionId}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Redis get session error:', { sessionId, error });
      return null;
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      await this.client.del(`session:${sessionId}`);
    } catch (error) {
      logger.error('Redis delete session error:', { sessionId, error });
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      logger.error('Redis health check failed:', error);
      return false;
    }
  }

  // Get Redis client for advanced operations
  getClient(): Redis {
    return this.client;
  }
}

export default new RedisService(); 