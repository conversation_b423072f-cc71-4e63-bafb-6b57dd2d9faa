import { Request } from 'express';
import Stripe from 'stripe';

// Database Models
export interface User {
  id: string;
  uid: string;
  name?: string;
  stripe_customer_id?: string;
  stripe_account_id?: string;
  stripe_access_token?: string;
  stripe_refresh_token?: string;
  email: string;
  plan_type: 'free' | 'pro' | 'scale';
  slack_workspace_id?: string;
  slack_workspace_name?: string;
  slack_access_token?: string;
  slack_bot_token?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Survey {
  id: string;
  user_id: string;
  question_text: string;
  options: SurveyOption[];
  branding: SurveyBranding;
  slack_channel_id: string;
  slack_workspace_id: string;
  follow_up_enabled: boolean;
  tracking_enabled: boolean;
  is_active: boolean;
  created_at: Date;
}

export interface SurveyOption {
  id: string;
  text: string;
  value: string;
}

export interface SurveyBranding {
  company_name: string;
  logo_url?: string;
  primary_color: string;
  from_name: string;
  from_email: string;
}

export interface Event {
  id: string;
  user_id: string;
  stripe_event_id: string;
  event_type: string;
  customer_id: string;
  subscription_id: string;
  plan_name: string;
  mrr_lost: number;
  canceled_at: Date;
  processed_at: Date;
  customer_email?: string;
  customer_name?: string;
}

export interface SurveyResponse {
  id: string;
  event_id: string;
  survey_id: string;
  token: string;
  selected_option?: string;
  custom_text?: string;
  status: 'pending' | 'responded' | 'no_response';
  responded_at?: Date;
  slack_sent_at?: Date;
  follow_up_sent_at?: Date;
  no_response_at?: Date;
  email_opened_at?: Date;
  open_count: number;
  hover_data?: Record<string, number>;
  created_at?: Date;
}

export interface UsageLog {
  id: string;
  user_id: string;
  month_year: string;
  events_count: number;
  created_at: Date;
}

// API Request/Response Types
export interface AuthenticatedRequest extends Request {
  user?: User;
}

export interface WebhookRequest extends Request {
  body: Stripe.Event;
}

export interface SurveyResponseRequest extends Request {
  body: {
    selected_option: string;
    custom_text?: string;
  };
  params: {
    token: string;
  };
}

export interface CreateSurveyRequest extends Request {
  body: {
    question_text: string;
    options: SurveyOption[];
    branding: SurveyBranding;
    slack_channel_id: string;
    follow_up_enabled?: boolean;
    tracking_enabled?: boolean;
  };
}

// Stripe Types
export interface StripeSubscription extends Stripe.Subscription {
  customer: string;
  items: Stripe.ApiList<Stripe.SubscriptionItem>;
}

// Slack Types
export interface SlackMessage {
  channel: string;
  text: string;
  attachments?: SlackAttachment[];
}

export interface SlackAttachment {
  fields?: SlackField[];
  actions?: SlackAction[];
}

export interface SlackField {
  title: string;
  value: string;
  short: boolean;
}

export interface SlackAction {
  type: string;
  text: string;
  url?: string;
}

// Email Types
export interface EmailData {
  to: string;
  from: string;
  subject: string;
  template: string;
  templateData: Record<string, any>;
}

export interface SurveyEmailData extends EmailData {
  templateData: {
    customer_name: string;
    plan_name: string;
    survey_url: string;
    options: SurveyOption[];
    branding: SurveyBranding;
    tracking_pixel?: string;
  };
}

// Configuration Types
export interface AppConfig {
  port: number;
  nodeEnv: string;
  apiBaseUrl: string;
  surveyBaseUrl: string;
  dashboardUrl: string;
  databaseUrl: string;
  redisUrl: string;
  stripeSecretKey: string;
  stripeClientId: string;
  stripeWebhookSecret: string;
  stripeProPriceId: string;
  stripeScalePriceId: string;
  sendgridApiKey: string;
  sendgridFromEmail: string;
  slackBotToken: string;
  slackClientId: string;
  slackClientSecret: string;
  slackRedirectUri: string;
  jwtSecret: string;
  jwtExpiresIn: string;
  bcryptRounds: number;
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
  logLevel: string;
  logFile: string;
  maxFileSize: number;
  uploadPath: string;
  emailTemplatePath: string;

  // Security hardening additions
  allowedOrigins?: string[];
  jwtAccessExpires?: string;
  jwtRefreshExpires?: string;
  jwtAlg?: 'HS256' | 'HS384' | 'HS512';
  cookie?: {
    domain?: string;
    secure: boolean;
    httpOnly: true;
    sameSite: 'lax' | 'strict' | 'none';
  };
  rateLimits?: {
    auth: { max: number; windowMs: number };
    webhooks: { max: number; windowMs: number };
  };
}

// Service Types
export interface DatabaseService {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  query<T = any>(text: string, params?: any[]): Promise<{ rows: T[] }>;
}

export interface StripeService {
  verifyWebhookSignature(payload: string, signature: string): Stripe.Event;
  getCustomer(customerId: string): Promise<Stripe.Customer>;
  getSubscription(subscriptionId: string): Promise<StripeSubscription>;
  createCheckoutSession(userId: string, planType: string): Promise<Stripe.Checkout.Session>;
}

export interface EmailService {
  sendSurveyEmail(data: {
    surveyId: string;
    customerEmail: string;
    customerName?: string;
    stripeCustomerId?: string;
    surveyTitle: string;
  }): Promise<{ success: boolean; emailId?: string; error?: string }>;
  sendFollowUpEmail(response: SurveyResponse): Promise<void>;
  sendWelcomeEmail(user: User): Promise<void>;
}

export interface SlackService {
  sendNotification(response: SurveyResponse): Promise<void>;
  sendNoResponseNotification(response: SurveyResponse): Promise<void>;
}

export interface SurveyService {
  generateToken(): string;
  createResponse(eventId: string, surveyId: string): Promise<SurveyResponse>;
  getResponseByToken(token: string): Promise<SurveyResponse | null>;
  updateResponse(token: string, data: Partial<SurveyResponse>): Promise<void>;
  markAsNoResponse(responseId: string): Promise<void>;
}

export interface UsageService {
  checkUsageLimits(userId: string): Promise<{
    current: number;
    limit: number;
    percentage: number;
  }>;
  incrementUsage(userId: string): Promise<void>;
}

// Error Types
export class AppError extends Error {
  constructor(
    public statusCode: number,
    public override message: string,
    public isOperational = true
  ) {
    super(message);
    Object.setPrototypeOf(this, AppError.prototype);
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(400, message);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(401, message);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(403, message);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(404, message);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(429, message);
  }
}

// Utility Types
export type PlanType = 'free' | 'pro' | 'scale';

export type ResponseStatus = 'pending' | 'responded' | 'no_response';

export type EventType = 'customer.subscription.deleted' | 'customer.subscription.updated';

// Constants
export const PLAN_LIMITS: Record<PlanType, number> = {
  free: 25,
  pro: 500,
  scale: Infinity
};

export const EMOJI_MAP: Record<string, string> = {
  'too_expensive': '🔴',
  'missing_feature': '🟡',
  'switching_tool': '🟠',
  'no_longer_needed': '🟢',
  'something_else': '⚪'
}; 