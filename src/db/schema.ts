import { pgTable, serial, varchar, text, timestamp, boolean, integer, numeric, uuid, jsonb, uniqueIndex, index } from 'drizzle-orm/pg-core';

// Users table aligned with src/types User
export const users = pgTable('users', {
  id: uuid('id').defaultRandom().primaryKey(),
  uid: varchar('uid', { length: 255 }).notNull(),
  stripeCustomerId: varchar('stripe_customer_id', { length: 255 }),
  stripeAccountId: varchar('stripe_account_id', { length: 255 }),
  stripeAccessToken: varchar('stripe_access_token', { length: 255 }),
  stripeRefreshToken: varchar('stripe_refresh_token', { length: 255 }),
  email: varchar('email', { length: 255 }).notNull(),
  planType: varchar('plan_type', { length: 20 }).notNull().default('free'),
  slackWorkspaceId: varchar('slack_workspace_id', { length: 255 }),
  slackWorkspaceName: varchar('slack_workspace_name', { length: 255 }),
  slackAccessToken: varchar('slack_access_token', { length: 255 }),
  slackBotToken: varchar('slack_bot_token', { length: 255 }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
}, (t) => {
  return {
    usersEmailUnique: uniqueIndex('users_email_unique').on(t.email),
    usersUidUnique: uniqueIndex('users_uid_unique').on(t.uid),
    usersStripeCustomerIdx: index('users_stripe_cust_idx').on(t.stripeCustomerId),
  };
});

// Surveys aligned with src/types Survey
export const surveys = pgTable('surveys', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  questionText: text('question_text').notNull(),
  options: jsonb('options').$type<Array<{ id: string; text: string; value: string }>>().notNull(),
  branding: jsonb('branding').$type<{
    company_name: string;
    logo_url?: string;
    primary_color: string;
    from_name: string;
    from_email: string;
  }>().notNull(),
  slackChannelId: varchar('slack_channel_id', { length: 255 }).notNull(),
  slackWorkspaceId: varchar('slack_workspace_id', { length: 255 }).notNull(),
  followUpEnabled: boolean('follow_up_enabled').notNull().default(false),
  trackingEnabled: boolean('tracking_enabled').notNull().default(false),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
}, (t) => {
  return {
    surveysUserIdx: index('surveys_user_idx').on(t.userId),
    surveysActiveIdx: index('surveys_active_idx').on(t.isActive),
  };
});

// Events aligned with src/types Event
export const events = pgTable('events', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  stripeEventId: varchar('stripe_event_id', { length: 255 }).notNull(),
  eventType: varchar('event_type', { length: 255 }).notNull(),
  customerId: varchar('customer_id', { length: 255 }).notNull(),
  subscriptionId: varchar('subscription_id', { length: 255 }).notNull(),
  planName: varchar('plan_name', { length: 255 }).notNull(),
  mrrLost: numeric('mrr_lost', { precision: 12, scale: 2 }).notNull(),
  canceledAt: timestamp('canceled_at', { withTimezone: true }).notNull(),
  processedAt: timestamp('processed_at', { withTimezone: true }).notNull(),
  customerEmail: varchar('customer_email', { length: 255 }),
  customerName: varchar('customer_name', { length: 255 }),
}, (t) => {
  return {
    eventsStripeEventUnique: uniqueIndex('events_stripe_event_unique').on(t.stripeEventId),
    eventsUserIdx: index('events_user_idx').on(t.userId),
    eventsProcessedIdx: index('events_processed_idx').on(t.processedAt),
  };
});

// Responses aligned with src/types Response
export const responses = pgTable('responses', {
  id: uuid('id').defaultRandom().primaryKey(),
  eventId: uuid('event_id').notNull().references(() => events.id, { onDelete: 'cascade' }),
  surveyId: uuid('survey_id').notNull().references(() => surveys.id, { onDelete: 'cascade' }),
  token: varchar('token', { length: 255 }).notNull(),
  selectedOption: varchar('selected_option', { length: 255 }),
  customText: text('custom_text'),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
  respondedAt: timestamp('responded_at', { withTimezone: true }),
  slackSentAt: timestamp('slack_sent_at', { withTimezone: true }),
  followUpSentAt: timestamp('follow_up_sent_at', { withTimezone: true }),
  noResponseAt: timestamp('no_response_at', { withTimezone: true }),
  emailOpenedAt: timestamp('email_opened_at', { withTimezone: true }),
  openCount: integer('open_count').notNull().default(0),
  hoverData: jsonb('hover_data').$type<Record<string, number>>(),
}, (t) => {
  return {
    responsesTokenUnique: uniqueIndex('responses_token_unique').on(t.token),
    responsesSurveyIdx: index('responses_survey_idx').on(t.surveyId),
  };
});

// Usage logs aligned with src/types UsageLog
export const usageLogs = pgTable('usage_logs', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  monthYear: varchar('month_year', { length: 7 }).notNull(), // e.g., "2025-08"
  eventsCount: integer('events_count').notNull().default(0),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
}, (t) => {
  return {
    usageLogsUniquePerMonth: uniqueIndex('usage_logs_user_month_unique').on(t.userId, t.monthYear),
  };
});

// Webhook events for audit/debug (provider-agnostic)
export const webhookEvents = pgTable('webhook_events', {
  id: serial('id').primaryKey(),
  provider: varchar('provider', { length: 100 }).notNull(),
  eventId: varchar('event_id', { length: 255 }).notNull(),
  payload: text('payload').notNull(),
  receivedAt: timestamp('received_at', { withTimezone: true }).defaultNow().notNull(),
  processed: boolean('processed').notNull().default(false),
  processedAt: timestamp('processed_at', { withTimezone: true }),
}, (t) => {
  return {
    webhookEventUnique: uniqueIndex('webhook_events_event_unique').on(t.eventId),
  };
});