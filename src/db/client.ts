import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import config from '../config';
import logger from '../utils/logger';

let pool: Pool | null = null;
let db: ReturnType<typeof drizzle> | null = null;

export function getPool(): Pool {
  if (!pool) {
    if (!config.databaseUrl) {
      throw new Error('DATABASE_URL is not configured');
    }
    pool = new Pool({
      connectionString: config.databaseUrl,
      // Optional: ssl configuration for production environments
      ssl: config.nodeEnv === 'production' ? { rejectUnauthorized: false } : undefined,
      max: 10,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000,
    });

    pool.on('error', (err) => {
      logger.error('Unexpected PG client error', err);
    });
  }
  return pool;
}

export function getDb() {
  if (!db) {
    db = drizzle(getPool());
  }
  return db;
}

export async function closeDb(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    db = null;
  }
}