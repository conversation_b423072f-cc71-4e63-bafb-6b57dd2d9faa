import type { Request, Response, NextFunction } from 'express';
import { ZodError, type ZodSchema } from 'zod';

type Part = 'body' | 'query' | 'params' | 'headers';

export function validate(schema: Partial<Record<Part, ZodSchema<any>>>) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (schema.body) req.body = schema.body.parse(req.body);
      if (schema.query) req.query = schema.query.parse(req.query);
      if (schema.params) req.params = schema.params.parse(req.params);
      if (schema.headers) req.headers = schema.headers.parse(req.headers);
      return next();
    } catch (err) {
      if (err instanceof ZodError) {
        return res.status(400).json({
          error: 'Validation error',
          issues: err.issues.map((e) => ({
            path: e.path.join('.'),
            message: e.message,
            code: e.code,
          })),
        });
      }
      return res.status(400).json({
        error: 'Validation error',
        message: err instanceof Error ? err.message : String(err),
      });
    }
  };
}