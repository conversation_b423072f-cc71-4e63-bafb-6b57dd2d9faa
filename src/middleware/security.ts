import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import config from '../config';
import logger from '../utils/logger';

// Rate limiting configuration + builders wired to config
export const createRateLimiter = (windowMs: number, max: number, message?: string) => {
  return rateLimit({
    windowMs,
    max,
    message: message || 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        path: req.path,
        userAgent: req.get('User-Agent')
      });
      res.status(429).json({
        error: 'Rate limit exceeded',
        message: message || 'Too many requests from this IP, please try again later.'
      });
    }
  });
};

// Specific rate limiters using config.rateLimits if available
export const webhookRateLimiter = createRateLimiter(
  config.rateLimits?.webhooks.windowMs ?? 15 * 60 * 1000,
  config.rateLimits?.webhooks.max ?? 300,
  'Too many webhook requests'
);

export const apiRateLimiter = createRateLimiter(
  15 * 60 * 1000,
  1000,
  'Too many API requests'
);

export const authRateLimiter = createRateLimiter(
  config.rateLimits?.auth.windowMs ?? 15 * 60 * 1000,
  config.rateLimits?.auth.max ?? 100,
  'Too many authentication attempts'
);

// Security headers middleware
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  frameguard: { action: 'deny' }
});

// CORS configuration with ALLOWED_ORIGINS support
export const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    const dynamicAllowed = new Set<string>([
      config.dashboardUrl,
      config.surveyBaseUrl,
      'http://localhost:3000',
      'http://localhost:3001',
      ...(config.allowedOrigins || []),
    ]);

    if (dynamicAllowed.has(origin)) {
      callback(null, true);
    } else {
      logger.warn('CORS blocked request', { origin });
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true as const,
  optionsSuccessStatus: 200
};

// Request validation middleware (legacy Joi-based; kept for compatibility)
export const validateRequest = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const { error } = schema.validate(req.body);
      if (error) {
        logger.warn('Request validation failed', {
          path: req.path,
          error: error.details[0].message
        });
        return res.status(400).json({
          error: 'Validation failed',
          message: error.details[0].message
        });
      }
      next();
    } catch (error) {
      logger.error('Request validation error:', error);
      res.status(500).json({ error: 'Internal validation error' });
    }
  };
};

// IP blocking middleware
export const ipBlocklist = (req: Request, res: Response, next: NextFunction) => {
  const blockedIPs = process.env.BLOCKED_IPS?.split(',') || [];
  const clientIP = req.ip;

  if (blockedIPs.includes(clientIP)) {
    logger.warn('Blocked IP attempted access', { ip: clientIP });
    return res.status(403).json({ error: 'Access denied' });
  }

  next();
};

// Request size limiting
export const requestSizeLimit = (req: Request, res: Response, next: NextFunction) => {
  const contentLength = parseInt(req.get('Content-Length') || '0');
  const maxSize = config.maxFileSize; // 5MB default

  if (contentLength > maxSize) {
    logger.warn('Request too large', {
      ip: req.ip,
      contentLength,
      maxSize
    });
    return res.status(413).json({ error: 'Request entity too large' });
  }

  next();
};

// Security logging middleware
export const securityLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - start;
    const statusCode = res.statusCode;

    // Log suspicious activities
    if (statusCode >= 400) {
      logger.warn('Suspicious request', {
        method: req.method,
        path: req.path,
        statusCode,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        duration
      });
    }

    // Log potential security issues
    if (req.path.includes('..') || req.path.includes('admin')) {
      logger.warn('Potential security issue', {
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
    }
  });

  next();
};

// API key validation middleware
export const validateApiKey = (req: Request, res: Response, next: NextFunction) => {
  const apiKey = req.headers['x-api-key'] as string;
  const validApiKey = process.env.API_KEY;

  if (!validApiKey) {
    return next(); // Skip if no API key is configured
  }

  if (!apiKey || apiKey !== validApiKey) {
    logger.warn('Invalid API key attempt', {
      ip: req.ip,
      path: req.path
    });
    return res.status(401).json({ error: 'Invalid API key' });
  }

  next();
};