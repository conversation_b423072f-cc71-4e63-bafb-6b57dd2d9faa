-- Initial database schema for Churn-Exit Survey Bot

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  uid TEXT UNIQUE NOT NULL,
  stripe_customer_id TEXT UNIQUE,
  stripe_account_id TEXT,
  email TEXT NOT NULL,
  plan_type TEXT DEFAULT 'free' CHECK (plan_type IN ('free', 'pro', 'scale')),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Surveys table
CREATE TABLE surveys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  question_text TEXT NOT NULL,
  options JSONB NOT NULL,
  branding JSONB NOT NULL,
  slack_channel_id TEXT NOT NULL,
  slack_workspace_id TEXT NOT NULL,
  follow_up_enabled BOOLEAN DEFAULT false,
  tracking_enabled BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Events table
CREATE TABLE events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  stripe_event_id TEXT UNIQUE NOT NULL,
  event_type TEXT NOT NULL,
  customer_id TEXT NOT NULL,
  subscription_id TEXT NOT NULL,
  plan_name TEXT,
  mrr_lost DECIMAL(10,2) DEFAULT 0,
  canceled_at TIMESTAMP NOT NULL,
  processed_at TIMESTAMP DEFAULT NOW(),
  customer_email TEXT,
  customer_name TEXT
);

-- Responses table
CREATE TABLE responses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  survey_id UUID NOT NULL REFERENCES surveys(id) ON DELETE CASCADE,
  token TEXT UNIQUE NOT NULL,
  selected_option TEXT,
  custom_text TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'responded', 'no_response')),
  responded_at TIMESTAMP,
  slack_sent_at TIMESTAMP,
  follow_up_sent_at TIMESTAMP,
  no_response_at TIMESTAMP,
  email_opened_at TIMESTAMP,
  open_count INTEGER DEFAULT 0,
  hover_data JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Usage logs table
CREATE TABLE usage_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  month_year TEXT NOT NULL,
  events_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, month_year)
);

-- Create indexes for performance
CREATE INDEX idx_users_uid ON users(uid);
CREATE INDEX idx_users_stripe_customer_id ON users(stripe_customer_id);
CREATE INDEX idx_surveys_user_id ON surveys(user_id);
CREATE INDEX idx_surveys_active ON surveys(user_id, is_active) WHERE is_active = true;
CREATE INDEX idx_events_user_id ON events(user_id);
CREATE INDEX idx_events_stripe_event_id ON events(stripe_event_id);
CREATE INDEX idx_events_canceled_at ON events(canceled_at);
CREATE INDEX idx_responses_token ON responses(token);
CREATE INDEX idx_responses_status ON responses(status);
CREATE INDEX idx_responses_pending ON responses(status, created_at) WHERE status = 'pending';
CREATE INDEX idx_usage_logs_user_month ON usage_logs(user_id, month_year);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 