import { pgTable, uuid, varchar, text, timestamp, integer, boolean, jsonb, index, uniqueIndex } from 'drizzle-orm/pg-core';

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  name: varchar('name', { length: 255 }),
  plan_type: varchar('plan_type', { length: 20 }).notNull().default('free'),
  stripe_customer_id: varchar('stripe_customer_id', { length: 255 }),
  stripe_access_token: text('stripe_access_token'),
  stripe_refresh_token: text('stripe_refresh_token'),
  slack_workspace_id: varchar('slack_workspace_id', { length: 255 }),
  slack_workspace_name: varchar('slack_workspace_name', { length: 255 }),
  slack_access_token: text('slack_access_token'),
  slack_bot_token: text('slack_bot_token'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Surveys table
export const surveys = pgTable('surveys', {
  id: uuid('id').primaryKey().defaultRandom(),
  user_id: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  question_text: text('question_text').notNull(),
  options: jsonb('options').$type<Array<{ id: string; text: string; emoji?: string }>>().notNull(),
  branding: jsonb('branding').$type<{ logo?: string; colors?: { primary: string; secondary: string } }>(),
  slack_channel_id: varchar('slack_channel_id', { length: 255 }).notNull(),
  slack_workspace_id: varchar('slack_workspace_id', { length: 255 }).notNull(),
  is_active: boolean('is_active').notNull().default(true),
  created_at: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  userIdx: index('surveys_user_idx').on(table.user_id),
  activeIdx: index('surveys_active_idx').on(table.is_active),
}));

// Events table (Stripe webhook events)
export const events = pgTable('events', {
  id: uuid('id').primaryKey().defaultRandom(),
  user_id: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  stripe_event_id: varchar('stripe_event_id', { length: 255 }).notNull().unique(),
  event_type: varchar('event_type', { length: 100 }).notNull(),
  customer_id: varchar('customer_id', { length: 255 }),
  subscription_id: varchar('subscription_id', { length: 255 }),
  plan_name: varchar('plan_name', { length: 100 }),
  mrr_lost: integer('mrr_lost'),
  canceled_at: timestamp('canceled_at'),
  customer_email: varchar('customer_email', { length: 255 }),
  customer_name: varchar('customer_name', { length: 255 }),
  created_at: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  userIdx: index('events_user_idx').on(table.user_id),
  stripeEventIdx: index('events_stripe_event_idx').on(table.stripe_event_id),
  customerIdx: index('events_customer_idx').on(table.customer_id),
}));

// Survey responses table
export const responses = pgTable('responses', {
  id: uuid('id').primaryKey().defaultRandom(),
  event_id: uuid('event_id').notNull().references(() => events.id, { onDelete: 'cascade' }),
  survey_id: uuid('survey_id').notNull().references(() => surveys.id, { onDelete: 'cascade' }),
  token: varchar('token', { length: 255 }).notNull().unique(),
  selected_option: varchar('selected_option', { length: 255 }),
  custom_text: text('custom_text'),
  status: varchar('status', { length: 20 }).notNull().default('pending'), // pending, responded, no_response
  responded_at: timestamp('responded_at'),
  created_at: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  eventIdx: index('responses_event_idx').on(table.event_id),
  surveyIdx: index('responses_survey_idx').on(table.survey_id),
  tokenIdx: index('responses_token_idx').on(table.token),
  statusIdx: index('responses_status_idx').on(table.status),
}));

// Usage logs table for tracking monthly usage
export const usageLogs = pgTable('usage_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  user_id: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  month: varchar('month', { length: 7 }).notNull(), // YYYY-MM format
  events_count: integer('events_count').notNull().default(0),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  userMonthIdx: uniqueIndex('user_month_idx').on(table.user_id, table.month),
  userIdx: index('usage_logs_user_idx').on(table.user_id),
}));

// Email tracking table
export const emailTracking = pgTable('email_tracking', {
  id: uuid('id').primaryKey().defaultRandom(),
  response_id: uuid('response_id').notNull().references(() => responses.id, { onDelete: 'cascade' }),
  email_sent_at: timestamp('email_sent_at').notNull(),
  email_opened_at: timestamp('email_opened_at'),
  link_clicked_at: timestamp('link_clicked_at'),
  created_at: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  responseIdx: index('email_tracking_response_idx').on(table.response_id),
})); 