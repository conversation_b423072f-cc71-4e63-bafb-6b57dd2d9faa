import dotenv from 'dotenv';
import { AppConfig } from '../types';

// Load environment variables
dotenv.config();

const nodeEnv = process.env['NODE_ENV'] || 'development';
const isProduction = nodeEnv === 'production';

// parse helper for rate limit env in form "count:window", e.g. "100:15m"
function parseRateLimitEnv(value: string | undefined, fallbackCount: number, fallbackWindowMs: number) {
  if (!value) return { max: fallbackCount, windowMs: fallbackWindowMs };
  try {
    const [countStr, windowStr] = value.split(':');
    const max = parseInt(countStr, 10);
    // support 15m, 1h, 30s
    const unit = windowStr.slice(-1);
    const qty = parseInt(windowStr.slice(0, -1), 10);
    const mult = unit === 'h' ? 60 * 60 * 1000 : unit === 'm' ? 60 * 1000 : unit === 's' ? 1000 : 1;
    const windowMs = qty * mult;
    if (!isFinite(max) || !isFinite(windowMs)) throw new Error('Invalid rate limit values');
    return { max, windowMs };
  } catch {
    return { max: fallbackCount, windowMs: fallbackWindowMs };
  }
}

const allowedOrigins = (process.env['ALLOWED_ORIGINS'] || '')
  .split(',')
  .map(s => s.trim())
  .filter(Boolean);

const jwtAccessExpires = process.env['JWT_ACCESS_EXPIRES'] || '15m';
const jwtRefreshExpires = process.env['JWT_REFRESH_EXPIRES'] || '7d';
const jwtAlg = (process.env['JWT_ALG'] as 'HS256' | 'HS384' | 'HS512') || 'HS256';

const rateAuth = parseRateLimitEnv(process.env['RATE_LIMIT_AUTH'], 100, 15 * 60 * 1000);
const rateWebhooks = parseRateLimitEnv(process.env['RATE_LIMIT_WEBHOOKS'], 300, 15 * 60 * 1000);

const cookieDomain = process.env['COOKIE_DOMAIN'] || undefined;
const cookieSecure = isProduction ? process.env['COOKIE_SECURE'] !== 'false' : false;

const config: AppConfig = {
  port: parseInt(process.env['PORT'] || '4000', 10),
  nodeEnv,
  apiBaseUrl: process.env['API_BASE_URL'] || 'http://localhost:4000',
  surveyBaseUrl: process.env['SURVEY_BASE_URL'] || 'http://localhost:4000',
  dashboardUrl: process.env['DASHBOARD_URL'] || 'http://localhost:4000',
  databaseUrl: process.env['DATABASE_URL'] || '',
  redisUrl: process.env['REDIS_URL'] || 'redis://localhost:6379',
  stripeSecretKey: process.env['STRIPE_SECRET_KEY'] || '',
  stripeClientId: process.env['STRIPE_CLIENT_ID'] || '',
  stripeWebhookSecret: process.env['STRIPE_WEBHOOK_SECRET'] || '',
  stripeProPriceId: process.env['STRIPE_PRO_PRICE_ID'] || '',
  stripeScalePriceId: process.env['STRIPE_SCALE_PRICE_ID'] || '',
  sendgridApiKey: process.env['SENDGRID_API_KEY'] || '',
  sendgridFromEmail: process.env['SENDGRID_FROM_EMAIL'] || '<EMAIL>',
  slackBotToken: process.env['SLACK_BOT_TOKEN'] || '',
  slackClientId: process.env['SLACK_CLIENT_ID'] || '',
  slackClientSecret: process.env['SLACK_CLIENT_SECRET'] || '',
  slackRedirectUri: process.env['SLACK_REDIRECT_URI'] || '',
  jwtSecret: process.env['JWT_SECRET'] || '',
  jwtExpiresIn: process.env['JWT_EXPIRES_IN'] || '7d',
  bcryptRounds: parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10),
  rateLimitWindowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000', 10),
  rateLimitMaxRequests: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100', 10),
  logLevel: process.env['LOG_LEVEL'] || 'info',
  logFile: process.env['LOG_FILE'] || 'logs/app.log',
  maxFileSize: parseInt(process.env['MAX_FILE_SIZE'] || '5242880', 10),
  uploadPath: process.env['UPLOAD_PATH'] || 'uploads/',
  emailTemplatePath: process.env['EMAIL_TEMPLATE_PATH'] || 'templates/emails/',

  // security additions
  allowedOrigins,
  jwtAccessExpires,
  jwtRefreshExpires,
  jwtAlg,
  cookie: {
    domain: cookieDomain,
    secure: cookieSecure,
    httpOnly: true,
    sameSite: isProduction ? ('lax' as const) : ('lax' as const),
  },
  rateLimits: {
    auth: rateAuth,
    webhooks: rateWebhooks,
  },
};

// Validate required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'STRIPE_SECRET_KEY',
  'STRIPE_CLIENT_ID',
  'STRIPE_WEBHOOK_SECRET',
  'SENDGRID_API_KEY',
  'SLACK_BOT_TOKEN',
  'SLACK_CLIENT_ID',
  'SLACK_CLIENT_SECRET',
  'SLACK_REDIRECT_URI',
  'JWT_SECRET'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  if (isProduction) {
    // In production, fail fast
    throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
  } else {
    // In non-production, log clear warnings and continue
    // Use console.warn to avoid importing logger here and creating cycles on early boot
    console.warn(
      `[config] Non-production mode detected (${nodeEnv}). Missing environment variables: ${missingEnvVars.join(', ')}`
    );
    console.warn(
      '[config] Some features (billing, email, slack) may be disabled or non-functional locally until these are provided.'
    );
  }
}

export default config;