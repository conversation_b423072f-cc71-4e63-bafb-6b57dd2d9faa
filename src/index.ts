import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import config from './config';
import logger from './utils/logger';
import database from './services/database';
import redisService from './services/redis';
import jobProcessor from './services/jobProcessor';
import monitoringService from './services/monitoring';

// Import routes
import webhookRoutes from './routes/webhooks';
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import surveyRoutes from './routes/surveys';
import trackingRoutes from './routes/tracking';
import billingRoutes from './routes/billing';
import exportRoutes from './routes/export';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/requestLogger';
import { corsOptions, securityHeaders, webhookRateLimiter, authRateLimiter } from './middleware/security';
import { authenticateToken, optionalAuth } from './middleware/auth';

class App {
  public app: express.Application;

  constructor() {
    this.app = express();
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddlewares(): void {
    // Security middleware (centralized)
    this.app.use(securityHeaders);
    this.app.use(cors(corsOptions));

    // Compression
    this.app.use(compression());

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Global baseline rate limiting (coarse)
    const limiter = rateLimit({
      windowMs: config.rateLimitWindowMs,
      max: config.rateLimitMaxRequests,
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use(limiter);

    // Request logging
    this.app.use(requestLogger);

    // Health check endpoint
    this.app.get('/health', async (_req, res) => {
      try {
        const healthStatus = await monitoringService.getHealthStatus();
        const isHealthy = healthStatus.database && healthStatus.redis;
        
        res.status(isHealthy ? 200 : 503).json({
          status: isHealthy ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          services: {
            database: healthStatus.database,
            redis: healthStatus.redis,
            external: healthStatus.externalServices
          },
          memory: {
            used: Math.round(healthStatus.memoryUsage.heapUsed / 1024 / 1024),
            total: Math.round(healthStatus.memoryUsage.heapTotal / 1024 / 1024),
            rss: Math.round(healthStatus.memoryUsage.rss / 1024 / 1024)
          }
        });
      } catch (error) {
        logger.error('Health check error:', error);
        res.status(503).json({
          status: 'error',
          timestamp: new Date().toISOString(),
          error: 'Health check failed'
        });
      }
    });

    // Metrics endpoint (for monitoring)
    this.app.get('/metrics', async (_req, res) => {
      try {
        const report = await monitoringService.generateReport();
        res.json(report);
      } catch (error) {
        logger.error('Metrics error:', error);
        res.status(500).json({ error: 'Failed to generate metrics' });
      }
    });
  }

  private initializeRoutes(): void {
    // API Routes with authentication
    this.app.use('/api/webhooks', webhookRoutes);
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/users', authenticateToken, userRoutes);
    this.app.use('/api/surveys', surveyRoutes); // Authentication handled in routes
    this.app.use('/api/billing', billingRoutes); // Authentication handled in routes
    this.app.use('/api/export', exportRoutes); // Authentication handled in routes
    this.app.use('/api/tracking', trackingRoutes);
    
    // Webhook routes (no /api prefix) with tighter rate limiter
    this.app.use('/webhooks', webhookRateLimiter, webhookRoutes);

    // Customer Portal redirect route
    this.app.use('/exit', (req, res) => {
      res.redirect(`${config.surveyBaseUrl}/survey${req.url}`);
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`
      });
    });
  }

  private initializeErrorHandling(): void {
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Connect to database
      await database.connect();
      logger.info('Database connected successfully');

      // Connect to Redis
      await redisService.connect();
      logger.info('Redis connected successfully');

      // Start background job processor
      await jobProcessor.start();
      logger.info('Background job processor started');

      // Start server
      const server = this.app.listen(config.port, () => {
        logger.info(`Server running on port ${config.port} in ${config.nodeEnv} mode`);
      });

      // Graceful shutdown
      const gracefulShutdown = async (signal: string) => {
        logger.info(`Received ${signal}. Starting graceful shutdown...`);
        
        server.close(async () => {
          logger.info('HTTP server closed');
          
          try {
            await jobProcessor.stop();
            await redisService.disconnect();
            await database.disconnect();
            logger.info('All services disconnected');
            process.exit(0);
          } catch (error) {
            logger.error('Error during shutdown:', error);
            process.exit(1);
          }
        });

        // Force shutdown after 30 seconds
        setTimeout(() => {
          logger.error('Forced shutdown after timeout');
          process.exit(1);
        }, 30000);
      };

      process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
      process.on('SIGINT', () => gracefulShutdown('SIGINT'));

      // Handle uncaught exceptions
      process.on('uncaughtException', (error) => {
        logger.error('Uncaught Exception:', error);
        process.exit(1);
      });

      process.on('unhandledRejection', (reason, promise) => {
        logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
        process.exit(1);
      });

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }
}

// Start the application
const app = new App();
app.start().catch((error) => {
  logger.error('Application startup failed:', error);
  process.exit(1);
});

export default app;