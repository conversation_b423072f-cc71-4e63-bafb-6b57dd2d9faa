import { Router, Request, Response } from 'express';
import Stripe from 'stripe';
import database from '../services/database';
import stripeService from '../services/stripe';
import emailService from '../services/email';
import slackService from '../services/slack-simple';
import surveyService from '../services/survey';
import usageService from '../services/usage';
import billingService from '../services/billing';
import { WebhookRequest, SurveyResponseRequest } from '../types';
import logger from '../utils/logger';
import { validate } from '../middleware/validate';
import { webhookParams, webhookStripeHeaders, surveyRespondParams, surveyRespondBody } from './schemas/webhooks';

const router = Router();

// Handle Stripe webhooks
router.post('/stripe', async (req: Request, res: Response) => {
  try {
    const signature = req.headers['stripe-signature'] as string;
    const event = stripeService.verifyWebhookSignature(req.body, signature);

    logger.info('Stripe webhook received', { 
      eventType: event.type, 
      eventId: event.id 
    });

    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await billingService.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.deleted':
        await billingService.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;

      case 'customer.subscription.trial_will_end':
        // Handle trial ending
        logger.info('Subscription trial ending', { 
          subscriptionId: event.data.object.id 
        });
        break;

      case 'invoice.payment_succeeded':
        // Handle successful payment
        logger.info('Payment succeeded', { 
          invoiceId: event.data.object.id 
        });
        break;

      case 'invoice.payment_failed':
        // Handle failed payment
        logger.warn('Payment failed', { 
          invoiceId: event.data.object.id 
        });
        break;

      default:
        logger.info('Unhandled Stripe webhook event', { 
          eventType: event.type 
        });
    }

    res.json({ received: true });
  } catch (error) {
    logger.error('Stripe webhook error:', error);
    res.status(400).json({ error: 'Webhook signature verification failed' });
  }
});

// Handle survey responses
router.post('/survey/respond/:token', validate({ params: surveyRespondParams, body: surveyRespondBody }), async (req: SurveyResponseRequest, res: Response) => {
  try {
    const { token } = req.params as any;
    const { selected_option, custom_text } = req.body;

    const response = await surveyService.getResponseByToken(token);
    if (!response || response.status === 'responded') {
      return res.status(404).json({ error: 'Survey already completed or invalid' });
    }

    await surveyService.updateResponse(token, {
      selected_option,
      custom_text,
      responded_at: new Date(),
      status: 'responded'
    });

    await slackService.sendNotification(response);

    res.json({ message: 'Thanks!' });
  } catch (error) {
    logger.error('Survey response processing error:', error);
    res.status(500).json({ error: 'Failed to process survey response' });
  }
});

// Handle email tracking pixel
router.get('/tracking/pixel/:token', validate({ params: surveyRespondParams }), async (req: Request, res: Response) => {
  try {
    const { token } = req.params as any;
    
    await recordEmailOpen(token);
    
    const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');
    res.writeHead(200, {
      'Content-Type': 'image/gif',
      'Content-Length': pixel.length.toString(),
      'Cache-Control': 'no-cache'
    });
    res.end(pixel);
  } catch (error) {
    logger.error('Tracking pixel error:', error);
    res.status(500).end();
  }
});

// Handle click/hover tracking
router.post('/tracking/hover/:token/:option', validate({ params: webhookParams.extend({ token: surveyRespondParams.shape.token, option: surveyRespondParams.shape.token }) }), async (req: Request, res: Response) => {
  try {
    const { token, option } = req.params as any;
    
    await recordOptionHover(token, option);
    
    res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Hover tracking error:', error);
    res.status(500).json({ error: 'Failed to record hover data' });
  }
});

// Helper functions
async function processSubscriptionCancellation(event: Stripe.Event, user: any): Promise<void> {
  const subscription = event.data.object as Stripe.Subscription;
  
  // Check usage limits before processing
  const usage = await billingService.checkUsageLimits(user.id);
  if (!usage.canProcess) {
    logger.info('Usage limit exceeded, skipping event processing', { 
      userId: user.id,
      current: usage.current,
      limit: usage.limit,
      planType: usage.planType
    });
    return;
  }

  const customer = await stripeService.getCustomer(subscription.customer as string);
  const subscriptionDetails = await stripeService.getSubscription(subscription.id);
  
  const mrrLost = stripeService.calculateMRR(subscriptionDetails);

  const eventRecord = await database.createEvent({
    user_id: user.id,
    stripe_event_id: event.id,
    event_type: event.type,
    customer_id: subscription.customer as string,
    subscription_id: subscription.id,
    plan_name: subscriptionDetails.items.data[0]?.price.nickname || 'Unknown',
    mrr_lost: mrrLost,
    canceled_at: new Date(subscription.canceled_at! * 1000),
    customer_email: customer.email,
    customer_name: customer.name
  });

  const survey = await database.getActiveSurvey(user.id);
  if (!survey) {
    logger.warn('No active survey found for user', { userId: user.id });
    return;
  }

  const token = surveyService.generateToken();
  await surveyService.createResponse(eventRecord.id, survey.id);

  // Check if user has white-label feature
  const hasWhiteLabel = await billingService.hasFeature(user.id, 'white_label_email');
  
  await emailService.sendSurveyEmail({
    surveyId: survey.id,
    customerEmail: eventRecord.customer_email || '<EMAIL>',
    customerName: eventRecord.customer_name,
    stripeCustomerId: eventRecord.customer_id,
    surveyTitle: survey.question_text || 'Survey',
    whiteLabel: hasWhiteLabel
  });

  // Increment usage after successful processing
  await billingService.incrementUsage(user.id);

  await usageService.incrementUsage(user.id);
  
  logger.info('Subscription cancellation processed', { 
    eventId: eventRecord.id, 
    userId: user.id,
    usageIncremented: true
  });
}

async function recordEmailOpen(token: string): Promise<void> {
  const response = await surveyService.getResponseByToken(token);
  if (response) {
    await surveyService.updateResponse(token, {
      email_opened_at: new Date(),
      open_count: (response.open_count || 0) + 1
    });
  }
}

async function recordOptionHover(token: string, option: string): Promise<void> {
  const response = await surveyService.getResponseByToken(token);
  if (response) {
    const hoverData = response.hover_data || {};
    hoverData[option] = (hoverData[option] || 0) + 1;
    
    await surveyService.updateResponse(token, {
      hover_data: hoverData
    });
  }
}

export default router;