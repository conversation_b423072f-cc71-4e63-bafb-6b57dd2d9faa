// tracking.ts
import { z } from 'zod';

export const trackingPixelParams = z.object({
  token: z.string().min(1),
});

export const trackingHoverParams = z.object({
  token: z.string().min(1),
  option: z.string().min(1),
});

export type TrackingPixelParams = z.infer<typeof trackingPixelParams>;
export type TrackingHoverParams = z.infer<typeof trackingHoverParams>;

// webhooks.ts
import { z as zw } from 'zod';

export const webhookParams = zw.object({
  uid: zw.string().min(1),
});

export const webhookStripeHeaders = zw.object({
  'stripe-signature': zw.string().min(1),
}).passthrough();

export const surveyRespondParams = zw.object({
  token: zw.string().min(1),
});

export const surveyRespondBody = zw.object({
  selected_option: zw.string().min(1),
  custom_text: zw.string().optional(),
});

// users.ts
import { z as zu } from 'zod';

export const paginationQuery = zu.object({
  limit: zu.coerce.number().int().positive().max(200).optional(),
  offset: zu.coerce.number().int().nonnegative().optional(),
});