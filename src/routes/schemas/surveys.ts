import { z } from 'zod';

export const createSurveyBody = z.object({
  question_text: z.string().min(1, 'question_text is required'),
  options: z
    .array(
      z.object({
        id: z.string().min(1),
        text: z.string().min(1),
        value: z.string().min(1),
      }),
    )
    .min(1, 'options must contain at least one option'),
  branding: z.object({
    company_name: z.string().min(1),
    logo_url: z.string().url().optional(),
    primary_color: z.string().min(1),
    from_name: z.string().min(1),
    from_email: z.string().email(),
  }),
  slack_channel_id: z.string().min(1),
  follow_up_enabled: z.boolean().optional(),
  tracking_enabled: z.boolean().optional(),
});

export const updateSurveyParams = z.object({
  id: z.string().min(1),
});

export const updateSurveyBody = z.object({
  question_text: z.string().min(1).optional(),
  options: z
    .array(
      z.object({
        id: z.string().min(1),
        text: z.string().min(1),
        value: z.string().min(1),
      }),
    )
    .optional(),
  branding: z
    .object({
      company_name: z.string().min(1),
      logo_url: z.string().url().optional(),
      primary_color: z.string().min(1),
      from_name: z.string().min(1),
      from_email: z.string().email(),
    })
    .optional(),
  slack_channel_id: z.string().min(1).optional(),
  follow_up_enabled: z.boolean().optional(),
  tracking_enabled: z.boolean().optional(),
  is_active: z.boolean().optional(),
});

export const getSurveyByIdParams = z.object({
  id: z.string().min(1),
});