import { z } from 'zod';

export const stripeOAuthQuery = z.object({
  state: z.string().optional(),
});

export const stripeOAuthCallbackQuery = z.object({
  code: z.string().min(1, 'Authorization code is required'),
  state: z.string().optional(),
});

export const slackOAuthQuery = z.object({
  state: z.string().optional(),
});

export const slackOAuthCallbackQuery = z.object({
  code: z.string().min(1, 'Authorization code is required'),
  state: z.string().min(1, 'State (user identifier) is required'),
});

export type StripeOAuthQuery = z.infer<typeof stripeOAuthQuery>;
export type StripeOAuthCallbackQuery = z.infer<typeof stripeOAuthCallbackQuery>;
export type SlackOAuthQuery = z.infer<typeof slackOAuthQuery>;
export type SlackOAuthCallbackQuery = z.infer<typeof slackOAuthCallbackQuery>;