import { z } from 'zod';

export const webhookParams = z.object({
  uid: z.string().min(1),
});

export const webhookStripeHeaders = z
  .object({
    'stripe-signature': z.string().min(1),
  })
  .passthrough();

export const surveyRespondParams = z.object({
  token: z.string().min(1),
});

export const surveyRespondBody = z.object({
  selected_option: z.string().min(1),
  custom_text: z.string().optional(),
});