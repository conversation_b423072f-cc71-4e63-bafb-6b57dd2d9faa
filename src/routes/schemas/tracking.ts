import { z } from 'zod';

/**
 * These schemas reflect the params actually sent to tracking endpoints.
 * We accept event_id, survey_id, and token for the pixel, and include
 * hover_ms (string) for hover tracking as it's read from query params.
 */
export const trackingPixelParams = z.object({
  event_id: z.string().min(1),
  survey_id: z.string().min(1),
  token: z.string().min(1),
});

export const trackingHoverParams = z.object({
  event_id: z.string().min(1),
  survey_id: z.string().min(1),
  token: z.string().min(1),
  hover_ms: z.string().regex(/^\d+$/, 'hover_ms must be a numeric string'),
});

export type TrackingPixelParams = z.infer<typeof trackingPixelParams>;
export type TrackingHoverParams = z.infer<typeof trackingHoverParams>;