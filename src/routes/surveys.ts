import { Router, Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import database from '../services/database';
import surveyService from '../services/survey';
import emailService from '../services/email';
import logger from '../utils/logger';
import config from '../config';
import { authenticateToken, requirePlan, optionalAuth } from '../middleware/auth';
import { AuthenticatedRequest } from '../types';

const router = Router();

// Create a new survey (requires authentication)
router.post('/', authenticateToken, requirePlan('free'), async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { title, questions, branding, followUpEnabled, trackingEnabled } = req.body;

    if (!title || !questions) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'title and questions are required'
      });
    }

    const survey = await surveyService.createSurvey({
      userId: req.user!.id,
      title,
      questions,
      branding: branding || {},
      followUpEnabled: followUpEnabled || false,
      trackingEnabled: trackingEnabled || false
    });

    logger.info('Survey created', { surveyId: survey.id, userId: req.user!.id });
    res.status(201).json({ success: true, survey });
  } catch (error) {
    logger.error('Failed to create survey:', error);
    res.status(500).json({ error: 'Failed to create survey' });
  }
});

// Get all surveys for a user (requires authentication)
router.get('/user/:userId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;
    
    // Ensure user can only access their own surveys
    if (req.user!.id !== userId) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    const surveys = await database.getSurveysByUserId(userId);
    
    res.json({ success: true, surveys });
  } catch (error) {
    logger.error('Failed to get surveys:', error);
    res.status(500).json({ error: 'Failed to get surveys' });
  }
});

// Get a specific survey (requires authentication)
router.get('/:id', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const survey = await database.getSurveyById(id);
    
    if (!survey) {
      return res.status(404).json({ error: 'Survey not found' });
    }
    
    // Ensure user can only access their own surveys
    if (survey.user_id !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    res.json({ success: true, survey });
  } catch (error) {
    logger.error('Failed to get survey:', error);
    res.status(500).json({ error: 'Failed to get survey' });
  }
});

// Update a survey (requires authentication)
router.put('/:id', authenticateToken, requirePlan('free'), async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { title, questions, branding, followUpEnabled, trackingEnabled } = req.body;

    const survey = await database.getSurveyById(id);
    if (!survey) {
      return res.status(404).json({ error: 'Survey not found' });
    }

    // Ensure user can only update their own surveys
    if (survey.user_id !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const updatedSurvey = await surveyService.updateSurvey(id, {
      title,
      questions,
      branding,
      followUpEnabled,
      trackingEnabled
    });

    logger.info('Survey updated', { surveyId: id, userId: req.user!.id });
    res.json({ success: true, survey: updatedSurvey });
  } catch (error) {
    logger.error('Failed to update survey:', error);
    res.status(500).json({ error: 'Failed to update survey' });
  }
});

// Delete a survey (requires authentication)
router.delete('/:id', authenticateToken, requirePlan('free'), async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const survey = await database.getSurveyById(id);
    if (!survey) {
      return res.status(404).json({ error: 'Survey not found' });
    }

    // Ensure user can only delete their own surveys
    if (survey.user_id !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    await surveyService.deleteSurvey(id);
    
    logger.info('Survey deleted', { surveyId: id, userId: req.user!.id });
    res.json({ success: true, message: 'Survey deleted successfully' });
  } catch (error) {
    logger.error('Failed to delete survey:', error);
    res.status(500).json({ error: 'Failed to delete survey' });
  }
});

// Get survey responses (requires authentication)
router.get('/:id/responses', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 50, status } = req.query;
    
    const survey = await database.getSurveyById(id);
    if (!survey) {
      return res.status(404).json({ error: 'Survey not found' });
    }

    // Ensure user can only access their own survey responses
    if (survey.user_id !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const responses = await database.getResponsesBySurveyId(id, {
      page: Number(page),
      limit: Number(limit),
      status: status as string
    });

    res.json({ success: true, responses });
  } catch (error) {
    logger.error('Failed to get survey responses:', error);
    res.status(500).json({ error: 'Failed to get survey responses' });
  }
});

// Generate survey URL for a specific customer (requires authentication)
router.post('/:id/generate-url', authenticateToken, requirePlan('free'), async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { customerEmail, customerName, stripeCustomerId } = req.body;

    const survey = await database.getSurveyById(id);
    if (!survey) {
      return res.status(404).json({ error: 'Survey not found' });
    }

    // Ensure user can only generate URLs for their own surveys
    if (survey.user_id !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const surveyUrl = await surveyService.generateSurveyUrl(id, {
      customerEmail,
      customerName,
      stripeCustomerId
    });

    res.json({ 
      success: true, 
      surveyUrl,
      message: 'Survey URL generated successfully'
    });
  } catch (error) {
    logger.error('Failed to generate survey URL:', error);
    res.status(500).json({ error: 'Failed to generate survey URL' });
  }
});

// Send survey email (requires authentication)
router.post('/:id/send-email', authenticateToken, requirePlan('free'), async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { customerEmail, customerName, stripeCustomerId } = req.body;

    const survey = await database.getSurveyById(id);
    if (!survey) {
      return res.status(404).json({ error: 'Survey not found' });
    }

    // Ensure user can only send emails for their own surveys
    if (survey.user_id !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const emailResult = await emailService.sendSurveyEmail({
      surveyId: id,
      customerEmail,
      customerName,
      stripeCustomerId,
      surveyTitle: survey.question_text
    });

    if (emailResult.success) {
      logger.info('Survey email sent', { 
        surveyId: id, 
        customerEmail,
        userId: req.user!.id,
        emailId: emailResult.emailId 
      });
      res.json({ 
        success: true, 
        message: 'Survey email sent successfully',
        emailId: emailResult.emailId
      });
    } else {
      res.status(400).json({ 
        error: 'Failed to send email',
        message: emailResult.error 
      });
    }
  } catch (error) {
    logger.error('Failed to send survey email:', error);
    res.status(500).json({ error: 'Failed to send survey email' });
  }
});

export default router;