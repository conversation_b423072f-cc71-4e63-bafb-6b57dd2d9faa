import { Router, Request, Response } from 'express';
import database from '../services/database';
import usageService from '../services/usage';
import { AuthenticatedRequest } from '../types';
import logger from '../utils/logger';
import { validate } from '../middleware/validate';
import { paginationQuery } from './schemas/users';

const router = Router();

// Get current user
router.get('/me', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const user = await database.getUserById(req.user.id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    logger.error('Get user error:', error);
    res.status(500).json({ error: 'Failed to get user' });
  }
});

// Get user usage
router.get('/usage', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const usage = await usageService.checkUsageLimits(req.user.id);
    const upgradePrompt = await usageService.getUpgradePrompt(req.user.id);

    res.json({
      usage,
      upgradePrompt
    });
  } catch (error) {
    logger.error('Get usage error:', error);
    res.status(500).json({ error: 'Failed to get usage' });
  }
});

// Get user events
router.get('/events', validate({ query: paginationQuery }), async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const limit = (req.query as any).limit ?? 50;
    const offset = (req.query as any).offset ?? 0;

    const events = await database.getEventsByUserId(req.user.id, limit, offset);

    res.json({
      events,
      pagination: {
        limit,
        offset,
        hasMore: events.length === limit
      }
    });
  } catch (error) {
    logger.error('Get events error:', error);
    res.status(500).json({ error: 'Failed to get events' });
  }
});

// Export events (Pro plan only)
router.get('/events/export', async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (req.user.plan_type === 'free') {
      return res.status(403).json({ error: 'CSV export requires Pro plan' });
    }

    // This would generate and return a CSV file
    res.json({ message: 'CSV export endpoint (Pro plan only)' });
  } catch (error) {
    logger.error('Export events error:', error);
    res.status(500).json({ error: 'Failed to export events' });
  }
});

export default router;