import { Router, Request, Response } from 'express';
import surveyService from '../services/survey';
import logger from '../utils/logger';
import { validate } from '../middleware/validate';
import { trackingPixelParams, trackingHoverParams } from './schemas/tracking';

const router = Router();

// Email tracking pixel
router.get('/pixel/:token', validate({ params: trackingPixelParams }), async (req: Request, res: Response) => {
  try {
    const { token } = req.params as any;
    
    const response = await surveyService.getResponseByToken(token);
    if (response) {
      await surveyService.updateResponse(token, {
        email_opened_at: new Date(),
        open_count: (response.open_count || 0) + 1
      });
    }
    
    const pixel = Buffer.from('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7', 'base64');
    res.writeHead(200, {
      'Content-Type': 'image/gif',
      'Content-Length': pixel.length.toString(),
      'Cache-Control': 'no-cache'
    });
    res.end(pixel);
  } catch (error) {
    logger.error('Tracking pixel error:', error);
    res.status(500).end();
  }
});

// Hover/click tracking for heat-map analytics
router.post('/hover/:token/:option', validate({ params: trackingHoverParams }), async (req: Request, res: Response) => {
  try {
    const { token, option } = req.params as any;
    
    const response = await surveyService.getResponseByToken(token);
    if (response) {
      const hoverData = response.hover_data || {};
      hoverData[option] = (hoverData[option] || 0) + 1;
      
      await surveyService.updateResponse(token, {
        hover_data: hoverData
      });
    }
    
    res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Hover tracking error:', error);
    res.status(500).json({ error: 'Failed to record hover data' });
  }
});

export default router;