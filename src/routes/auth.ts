import { Router, Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import database from '../services/database';
import stripeService from '../services/stripe';
import emailService from '../services/email';
import { generateTokens } from '../middleware/auth';
import logger from '../utils/logger';
import config from '../config';

const router = Router();

// Stripe OAuth initiation
router.get('/stripe/oauth', async (req: Request, res: Response) => {
  try {
    const { state } = req.query;
    
    // Generate OAuth state for security
    const oauthState = state || uuidv4();
    
    const oauthUrl = `https://connect.stripe.com/oauth/authorize?` +
      `client_id=${config.stripeClientId}&` +
      `response_type=code&` +
      `scope=read_write&` +
      `state=${oauthState}`;

    logger.info('Stripe OAuth initiated', { state: oauthState });
    
    res.json({ 
      success: true, 
      oauthUrl, 
      state: oauthState,
      message: 'Redirect user to this URL to connect Stripe account' 
    });
  } catch (error) {
    logger.error('Stripe OAuth URL generation failed:', error);
    res.status(500).json({ success: false, error: 'Failed to generate OAuth URL' });
  }
});

// Stripe OAuth callback
router.get('/stripe/oauth/callback', async (req: Request, res: Response) => {
  try {
    const { code, state } = req.query;

    if (!code) {
      return res.status(400).json({ error: 'Authorization code required' });
    }

    // Exchange code for access token
    const tokenResult = await stripeService.exchangeCodeForToken(code as string);
    
    if (!tokenResult.success) {
      logger.error('Stripe token exchange failed:', tokenResult.error);
      return res.status(400).json({ error: 'Failed to exchange authorization code' });
    }

    // Get Stripe account details
    const accountDetails = await stripeService.getAccountDetails(tokenResult.accessToken!);
    
    if (!accountDetails.success) {
      logger.error('Failed to get Stripe account details:', accountDetails.error);
      return res.status(400).json({ error: 'Failed to get account details' });
    }

    // Check if user already exists
    let user = await database.getUserByStripeCustomerId(accountDetails.account.id);
    
    if (!user) {
      // Create new user
      const uid = uuidv4();
      user = await database.createUser({
        uid,
        email: accountDetails.account.email || `stripe_${accountDetails.account.id}@example.com`,
        plan_type: 'free',
        stripe_customer_id: accountDetails.account.id,
        stripe_account_id: accountDetails.account.id,
        stripe_access_token: tokenResult.accessToken,
        stripe_refresh_token: tokenResult.refreshToken,
        created_at: new Date(),
        updated_at: new Date()
      });

      logger.info('New user created via Stripe OAuth', { 
        userId: user.id, 
        stripeAccountId: accountDetails.account.id 
      });

      // Send welcome email
      try {
        await emailService.sendWelcomeEmail(user);
      } catch (emailError) {
        logger.warn('Failed to send welcome email:', emailError);
      }
    } else {
      // Update existing user's Stripe tokens
      await database.updateUser(user.id, {
        stripe_access_token: tokenResult.accessToken,
        stripe_refresh_token: tokenResult.refreshToken,
        updated_at: new Date()
      });

      logger.info('Existing user updated via Stripe OAuth', { 
        userId: user.id, 
        stripeAccountId: accountDetails.account.id 
      });
    }

    // Generate JWT tokens
    const tokens = generateTokens(user);

    // Redirect to dashboard with tokens
    const redirectUrl = `${config.dashboardUrl}/auth/callback?` +
      `accessToken=${tokens.accessToken}&` +
      `refreshToken=${tokens.refreshToken}&` +
      `provider=stripe`;

    res.redirect(redirectUrl);

  } catch (error) {
    logger.error('Stripe OAuth callback error:', error);
    res.status(500).json({ error: 'OAuth callback failed' });
  }
});

// Slack OAuth initiation
router.get('/slack/oauth', async (req: Request, res: Response) => {
  try {
    const { state } = req.query;
    
    // Generate OAuth state for security
    const oauthState = state || uuidv4();
    
    const oauthUrl = `https://slack.com/oauth/v2/authorize?` +
      `client_id=${config.slackClientId}&` +
      `scope=chat:write,channels:read,users:read&` +
      `state=${oauthState}&` +
      `redirect_uri=${encodeURIComponent(config.slackRedirectUri)}`;

    logger.info('Slack OAuth initiated', { state: oauthState });
    
    res.json({ 
      success: true, 
      oauthUrl, 
      state: oauthState,
      message: 'Redirect user to this URL to connect Slack workspace' 
    });
  } catch (error) {
    logger.error('Slack OAuth URL generation failed:', error);
    res.status(500).json({ success: false, error: 'Failed to generate OAuth URL' });
  }
});

// Slack OAuth callback
router.get('/slack/oauth/callback', async (req: Request, res: Response) => {
  try {
    const { code, state } = req.query;

    if (!code) {
      return res.status(400).json({ error: 'Authorization code required' });
    }

    // Exchange code for access token
    const tokenResult = await stripeService.exchangeSlackCodeForToken(code as string);
    
    if (!tokenResult.success) {
      logger.error('Slack token exchange failed:', tokenResult.error);
      return res.status(400).json({ error: 'Failed to exchange authorization code' });
    }

    // Get Slack workspace details
    const workspaceDetails = await stripeService.getSlackWorkspaceDetails(tokenResult.accessToken!);
    
    if (!workspaceDetails.success) {
      logger.error('Failed to get Slack workspace details:', workspaceDetails.error);
      return res.status(400).json({ error: 'Failed to get workspace details' });
    }

    // For Slack OAuth, we typically need an existing user
    // This would usually be called after Stripe OAuth or user registration
    // For now, we'll create a placeholder user if none exists
    let user = await database.getUserByUid('slack-user'); // This should be the actual user ID
    
    if (!user) {
      // Create placeholder user (in real app, this would be the authenticated user)
      const uid = uuidv4();
      user = await database.createUser({
        uid,
        email: `slack_${workspaceDetails.workspace.id}@example.com`,
        plan_type: 'free',
        slack_workspace_id: workspaceDetails.workspace.id,
        slack_workspace_name: workspaceDetails.workspace.name,
        slack_access_token: tokenResult.accessToken,
        slack_bot_token: tokenResult.botToken,
        created_at: new Date(),
        updated_at: new Date()
      });

      logger.info('New user created via Slack OAuth', { 
        userId: user.id, 
        workspaceId: workspaceDetails.workspace.id 
      });
    } else {
      // Update existing user's Slack tokens
      await database.updateUser(user.id, {
        slack_workspace_id: workspaceDetails.workspace.id,
        slack_workspace_name: workspaceDetails.workspace.name,
        slack_access_token: tokenResult.accessToken,
        slack_bot_token: tokenResult.botToken,
        updated_at: new Date()
      });

      logger.info('Existing user updated via Slack OAuth', { 
        userId: user.id, 
        workspaceId: workspaceDetails.workspace.id 
      });
    }

    // Generate JWT tokens
    const tokens = generateTokens(user);

    // Redirect to dashboard with tokens
    const redirectUrl = `${config.dashboardUrl}/auth/callback?` +
      `accessToken=${tokens.accessToken}&` +
      `refreshToken=${tokens.refreshToken}&` +
      `provider=slack`;

    res.redirect(redirectUrl);

  } catch (error) {
    logger.error('Slack OAuth callback error:', error);
    res.status(500).json({ error: 'OAuth callback failed' });
  }
});

// Token refresh endpoint
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ error: 'Refresh token required' });
    }

    const decoded = require('jsonwebtoken').verify(refreshToken, config.jwtSecret) as any;
    
    if (decoded.type !== 'refresh') {
      return res.status(401).json({ error: 'Invalid refresh token' });
    }

    const user = await database.getUserById(decoded.userId);
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    const tokens = generateTokens(user);
    
    res.json({
      success: true,
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken
    });
  } catch (error) {
    logger.error('Token refresh error:', error);
    res.status(401).json({ error: 'Invalid refresh token' });
  }
});

// Logout endpoint
router.post('/logout', async (req: Request, res: Response) => {
  try {
    // In a real application, you might want to blacklist the token
    // For now, we'll just return success
    res.json({ success: true, message: 'Logged out successfully' });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({ error: 'Logout failed' });
  }
});

// Get current user info
router.get('/me', async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    const decoded = require('jsonwebtoken').verify(token, config.jwtSecret) as any;
    const user = await database.getUserById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({ error: 'User not found' });
    }

    // Don't return sensitive information
    const { stripe_access_token, stripe_refresh_token, slack_access_token, slack_bot_token, ...safeUser } = user;
    
    res.json({ success: true, user: safeUser });
  } catch (error) {
    logger.error('Get user info error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
});

export default router;