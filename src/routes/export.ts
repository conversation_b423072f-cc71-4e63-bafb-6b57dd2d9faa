import { Router, Request, Response } from 'express';
import { authenticateToken, requirePlan } from '../middleware/auth';
import { AuthenticatedRequest } from '../types';
import database from '../services/database';
import billingService from '../services/billing';
import logger from '../utils/logger';

const router = Router();

// Export survey responses as CSV (Scale plan only)
router.get('/survey/:surveyId/csv', authenticateToken, requirePlan('scale'), async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { surveyId } = req.params;
    const { startDate, endDate, status } = req.query;

    // Verify survey belongs to user
    const survey = await database.getSurveyById(surveyId);
    if (!survey) {
      return res.status(404).json({ error: 'Survey not found' });
    }

    if (survey.user_id !== req.user!.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get responses with optional filters
    const responses = await database.getResponsesBySurveyId(surveyId, {
      page: 1,
      limit: 10000, // Get all responses
      status: status as string
    });

    // Get associated events for customer data
    const responsesWithEvents = await Promise.all(
      responses.map(async (response) => {
        const event = await database.getEventById(response.event_id);
        return {
          ...response,
          customer_email: event?.customer_email || '',
          customer_name: event?.customer_name || ''
        };
      })
    );

    // Filter by date if provided
    let filteredResponses = responsesWithEvents;
    if (startDate || endDate) {
      const start = startDate ? new Date(startDate as string) : new Date(0);
      const end = endDate ? new Date(endDate as string) : new Date();
      
      filteredResponses = responsesWithEvents.filter(response => {
        const responseDate = new Date(response.created_at);
        return responseDate >= start && responseDate <= end;
      });
    }

    // Generate CSV content
    const csvHeaders = [
      'Response ID',
      'Customer Email',
      'Customer Name',
      'Selected Option',
      'Custom Text',
      'Status',
      'Created At',
      'Responded At'
    ];

    const csvRows = filteredResponses.map(response => [
      response.id,
      response.customer_email || '',
      response.customer_name || '',
      response.selected_option || '',
      response.custom_text || '',
      response.status,
      response.created_at,
      response.responded_at || ''
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(field => `"${field}"`).join(','))
    ].join('\n');

    // Set response headers for CSV download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="survey-${surveyId}-responses-${new Date().toISOString().split('T')[0]}.csv"`);
    
    res.send(csvContent);

    logger.info('CSV export completed', {
      surveyId,
      userId: req.user!.id,
      responseCount: filteredResponses.length
    });
  } catch (error) {
    logger.error('Failed to export CSV:', error);
    res.status(500).json({ error: 'Failed to export CSV' });
  }
});

// Export usage data as CSV (Scale plan only)
router.get('/usage/csv', authenticateToken, requirePlan('scale'), async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { startMonth, endMonth } = req.query;

    // Get usage logs for the user
    const usageLogs = await database.getUsageLogsByUserId(req.user!.id);

    // Filter by date range if provided
    let filteredLogs = usageLogs;
    if (startMonth || endMonth) {
      const start = startMonth as string || '2020-01';
      const end = endMonth as string || new Date().toISOString().slice(0, 7);
      
      filteredLogs = usageLogs.filter(log => {
        return log.month_year >= start && log.month_year <= end;
      });
    }

    // Generate CSV content
    const csvHeaders = [
      'Month',
      'Events Count',
      'Plan Type',
      'Limit',
      'Percentage Used',
      'Created At'
    ];

    const csvRows = filteredLogs.map(log => [
      log.month_year,
      log.events_count,
      req.user!.plan_type,
      req.user!.plan_type === 'free' ? 25 : req.user!.plan_type === 'pro' ? 500 : 'Unlimited',
      req.user!.plan_type === 'scale' ? 'N/A' : Math.round((log.events_count / (req.user!.plan_type === 'free' ? 25 : 500)) * 100),
      log.created_at
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(field => `"${field}"`).join(','))
    ].join('\n');

    // Set response headers for CSV download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="usage-${req.user!.id}-${new Date().toISOString().split('T')[0]}.csv"`);
    
    res.send(csvContent);

    logger.info('Usage CSV export completed', {
      userId: req.user!.id,
      logCount: filteredLogs.length
    });
  } catch (error) {
    logger.error('Failed to export usage CSV:', error);
    res.status(500).json({ error: 'Failed to export usage CSV' });
  }
});

export default router; 