import { Router, Request, Response } from 'express';
import billingService from '../services/billing';
import { authenticateToken, requirePlan } from '../middleware/auth';
import { AuthenticatedRequest } from '../types';
import logger from '../utils/logger';

const router = Router();

// Create checkout session for plan upgrade
router.post('/checkout/:planType', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { planType } = req.params;
    
    if (!['pro', 'scale'].includes(planType)) {
      return res.status(400).json({ error: 'Invalid plan type' });
    }

    const session = await billingService.createCheckoutSession(req.user!.id, planType as 'pro' | 'scale');
    
    res.json({
      success: true,
      checkoutUrl: session.url,
      sessionId: session.id
    });
  } catch (error) {
    logger.error('Failed to create checkout session:', error);
    res.status(500).json({ error: 'Failed to create checkout session' });
  }
});

// Create customer portal session
router.post('/portal', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const session = await billingService.createCustomerPortalSession(req.user!.id);
    
    res.json({
      success: true,
      portalUrl: session.url,
      sessionId: session.id
    });
  } catch (error) {
    logger.error('Failed to create portal session:', error);
    res.status(500).json({ error: 'Failed to create portal session' });
  }
});

// Get current usage and limits
router.get('/usage', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const usage = await billingService.checkUsageLimits(req.user!.id);
    
    res.json({
      success: true,
      usage
    });
  } catch (error) {
    logger.error('Failed to get usage:', error);
    res.status(500).json({ error: 'Failed to get usage' });
  }
});

// Get plan features
router.get('/features/:planType', async (req: Request, res: Response) => {
  try {
    const { planType } = req.params;
    
    if (!['free', 'pro', 'scale'].includes(planType)) {
      return res.status(400).json({ error: 'Invalid plan type' });
    }

    const features = await billingService.getPlanFeatures(planType as 'free' | 'pro' | 'scale');
    
    res.json({
      success: true,
      planType,
      features
    });
  } catch (error) {
    logger.error('Failed to get plan features:', error);
    res.status(500).json({ error: 'Failed to get plan features' });
  }
});

// Check if user has specific feature
router.get('/features/check/:feature', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { feature } = req.params;
    const hasFeature = await billingService.hasFeature(req.user!.id, feature);
    
    res.json({
      success: true,
      feature,
      hasFeature
    });
  } catch (error) {
    logger.error('Failed to check feature access:', error);
    res.status(500).json({ error: 'Failed to check feature access' });
  }
});

// Get plan limits
router.get('/limits', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const usage = await billingService.checkUsageLimits(req.user!.id);
    
    res.json({
      success: true,
      limits: {
        current: usage.current,
        limit: usage.limit,
        percentage: usage.percentage,
        planType: usage.planType,
        canProcess: usage.canProcess
      }
    });
  } catch (error) {
    logger.error('Failed to get limits:', error);
    res.status(500).json({ error: 'Failed to get limits' });
  }
});

export default router; 