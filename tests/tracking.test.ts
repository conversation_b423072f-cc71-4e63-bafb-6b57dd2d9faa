import { describe, it, expect } from 'vitest';
import { trackingPixelParams, trackingHoverParams } from '../src/routes/schemas/tracking';

describe('Tracking schema validation', () => {
  it('accepts valid tracking pixel params', () => {
    const params = {
      event_id: 'evt_123',
      survey_id: 'srv_456',
      token: 'tok_789',
    };
    const parsed = trackingPixelParams.parse(params);
    expect(parsed.event_id).toBe('evt_123');
  });

  it('rejects invalid tracking pixel params (missing token)', () => {
    const bad: any = {
      event_id: 'evt_123',
      survey_id: 'srv_456',
    };
    expect(() => trackingPixelParams.parse(bad)).toThrow();
  });

  it('accepts valid tracking hover params', () => {
    const params = {
      event_id: 'evt_123',
      survey_id: 'srv_456',
      token: 'tok_789',
      hover_ms: '2500',
    };
    const parsed = trackingHoverParams.parse(params);
    expect(parsed.hover_ms).toBe('2500');
  });

  it('rejects invalid tracking hover params (non-numeric hover_ms)', () => {
    const bad: any = {
      event_id: 'evt_123',
      survey_id: 'srv_456',
      token: 'tok_789',
      hover_ms: 'abc',
    };
    expect(() => trackingHoverParams.parse(bad)).toThrow();
  });
});