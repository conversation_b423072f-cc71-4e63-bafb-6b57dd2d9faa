import { describe, it, expect } from 'vitest';
import { createSurveyBody, updateSurveyBody } from '../src/routes/schemas/surveys';

describe('Survey schema validation', () => {
  it('accepts a valid create survey payload', () => {
    const valid = {
      question_text: 'Why did you cancel?',
      options: [
        { id: 'too_expensive', text: 'Too expensive', value: 'too_expensive' },
        { id: 'missing_feature', text: 'Missing feature', value: 'missing_feature' },
      ],
      branding: {
        company_name: 'Acme Inc.',
        primary_color: '#000000',
        from_name: 'Acme Support',
        from_email: '<EMAIL>',
      },
      slack_channel_id: 'C12345',
      follow_up_enabled: true,
      tracking_enabled: true,
    };
    const parsed = createSurveyBody.parse(valid);
    expect(parsed).toBeDefined();
  });

  it('rejects missing required fields', () => {
    const invalid: any = {
      options: [],
      branding: {},
      slack_channel_id: '',
    };
    expect(() => createSurveyBody.parse(invalid)).toThrow();
  });

  it('accepts partial update survey payload', () => {
    const update = {
      question_text: 'Updated question',
      tracking_enabled: false,
    };
    const parsed = updateSurveyBody.parse(update);
    expect(parsed).toBeDefined();
  });
});