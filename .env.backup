# Server Configuration
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000
SURVEY_BASE_URL=http://localhost:3000
DASHBOARD_URL=http://localhost:3000

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/churnbot
REDIS_URL=redis://localhost:6379

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PUBLISHABLE_KEY=pk_test_...

# SendGrid Configuration
SENDGRID_API_KEY=SG...
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Your Company Name

# Slack Configuration
SLACK_BOT_TOKEN=xoxb-...
SLACK_SIGNING_SECRET=...

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Email Templates
EMAIL_TEMPLATE_PATH=templates/emails/ DATABASE_URL=postgresql://postgres:password@localhost:5432/churnbot
REDIS_URL=redis://localhost:6379
